
// ==UserScript==
// @name         自动关闭麻豆网站弹窗
// @namespace    http://tampermonkey.net/
// @version      2.0
// @description  自动关闭麻豆弹窗 - 优化版
// @match        *://madouqu.com/*
// @match        *://madouqu*.com/*
// @match        *://madouqu*.xyz/*
// @match        *://*.madouqu*/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    const SELECTORS = 'button.swal2-close, .swal2-container';

    // 关闭弹窗
    const closePopup = () => {
        const btn = document.querySelector('button.swal2-close');
        if (btn) {
            console.log('[自动关闭] 发现弹窗，正在关闭...');
            btn.click();
            return true;
        }
        return false;
    };

    // 检查节点是否包含弹窗
    const hasPopup = (node) =>
        node.nodeType === 1 && (
            node.matches?.(SELECTORS) ||
            node.querySelector?.(SELECTORS)
        );

    // 设置观察器
    const setupObserver = () => {
        new MutationObserver(mutations => {
            if (mutations.some(m => 
                Array.from(m.addedNodes).some(hasPopup)
            )) {
                requestAnimationFrame(closePopup);
            }
        }).observe(document.documentElement, {
            childList: true,
            subtree: true
        });
    };

    // 初始化
    const init = () => {
        console.log('[自动关闭弹窗] 脚本已启动');
        closePopup();
        setupObserver();
    };

    // 启动
    document.readyState === 'loading'
        ? document.addEventListener('DOMContentLoaded', init)
        : init();

})();
