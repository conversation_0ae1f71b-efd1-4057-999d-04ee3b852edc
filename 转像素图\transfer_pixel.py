import tkinter as tk
from tkinter import filedialog, Scale, Label, Button, Frame, ttk, messagebox
from PIL import Image, ImageTk, ImageEnhance, ImageFilter
import numpy as np
from functools import lru_cache
import os
import subprocess
import platform

class PixelArtConverter:
    def __init__(self, root):
        self.root = root
        self.root.title("游戏像素风格转换器")
        self.root.geometry("900x700")
        
        # 设置窗口居中
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        window_width = 900
        window_height = 700
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # 设置主题颜色
        self.bg_color = "#f0f0f0"
        self.accent_color = "#4a90e2"
        self.root.configure(bg=self.bg_color)
        
        self.image_path = None
        self.original_image = None
        self.pixelated_image = None
        self.pixel_size = 10
        self.detail_level = 50
        self.saturation = 1.0
        self.current_style = "custom"  # 添加当前风格标记
        self.styles = {
            "custom": {"pixel": 10, "detail": 50, "saturation": 1.0},
            "细腻像素": {"pixel": 3, "detail": 85, "saturation": 1.25},
            "手绘像素": {"pixel": 5, "detail": 75, "saturation": 1.3},
            "16-bit": {"pixel": 4, "detail": 70, "saturation": 1.2},
            "8-bit": {"pixel": 8, "detail": 30, "saturation": 1.1},
            "现代像素": {"pixel": 6, "detail": 60, "saturation": 1.15},
            "复古像素": {"pixel": 12, "detail": 20, "saturation": 0.9},
            "极简像素": {"pixel": 15, "detail": 10, "saturation": 0.8}
        }
        self.last_save_path = None  # 添加保存路径记录
        
        self.update_timer = None
        self.focused_slider = None  # 记录当前焦点滑块
        self.setup_ui()
    
    def setup_ui(self):
        main_frame = Frame(self.root, bg=self.bg_color)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 图片显示区域
        self.image_frame = Frame(main_frame, bg=self.bg_color)
        self.image_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题样式
        title_style = {"font": ("微软雅黑", 12, "bold"), "bg": self.bg_color}
        
        self.original_label = Label(self.image_frame, text="原始图片", **title_style)
        self.original_label.grid(row=0, column=0, padx=10, pady=5)
        
        self.original_display = Label(self.image_frame, bg="white", width=40, height=20,
                                    relief="solid", borderwidth=1)
        self.original_display.grid(row=1, column=0, padx=10, pady=5)
        
        self.pixel_label = Label(self.image_frame, text="像素风格图片", **title_style)
        self.pixel_label.grid(row=0, column=1, padx=10, pady=5)
        
        self.pixel_display = Label(self.image_frame, bg="white", width=40, height=20,
                                 relief="solid", borderwidth=1)
        self.pixel_display.grid(row=1, column=1, padx=10, pady=5)
        
        # 添加风格选择区域（在控制区域之前）
        style_frame = Frame(main_frame, bg=self.bg_color)
        style_frame.pack(fill=tk.X, pady=10)
        
        Label(style_frame, text="预设风格:", font=("微软雅黑", 10, "bold"), 
              bg=self.bg_color).pack(side=tk.LEFT, padx=10)
        
        # 风格按钮样式
        style_btn_style = {
            "font": ("微软雅黑", 10),
            "bg": "#ffffff",
            "relief": "raised",
            "padx": 15,
            "pady": 3,
            "cursor": "hand2"
        }
        
        # 创建风格按钮
        self.style_buttons = {}
        for style_name in self.styles.keys():
            btn = Button(style_frame, text=style_name,
                        command=lambda s=style_name: self.apply_style(s),
                        **style_btn_style)
            btn.pack(side=tk.LEFT, padx=3)
            self.style_buttons[style_name] = btn
        
        # 高亮显示当前风格
        if self.current_style in self.style_buttons:
            self.style_buttons[self.current_style].configure(
                bg=self.accent_color, fg="white")
        
        # 控制区域（原有的滑块部分）
        control_frame = Frame(main_frame, bg=self.bg_color)
        control_frame.pack(fill=tk.X, pady=10)
        
        # 滑块样式
        slider_style = {
            "bg": self.bg_color,
            "activebackground": self.accent_color,
            "troughcolor": "#e0e0e0",
            "length": 200
        }
        
        # 滑块标签样式
        label_style = {"font": ("微软雅黑", 10), "bg": self.bg_color}
        
        # 像素大小滑块
        Label(control_frame, text="像素大小:", **label_style).pack(side=tk.LEFT, padx=10)
        self.pixel_slider = Scale(control_frame, from_=2, to=50, orient=tk.HORIZONTAL,
                                command=self.schedule_update, **slider_style)
        self.pixel_slider.set(self.pixel_size)
        self.pixel_slider.pack(side=tk.LEFT, padx=5)
        self.setup_slider_bindings(self.pixel_slider, 1)  # 添加键盘绑定
        
        # 细节保持滑块
        Label(control_frame, text="细节保持:", **label_style).pack(side=tk.LEFT, padx=10)
        self.detail_slider = Scale(control_frame, from_=0, to=100, orient=tk.HORIZONTAL,
                                 command=self.schedule_update, **slider_style)
        self.detail_slider.set(self.detail_level)
        self.detail_slider.pack(side=tk.LEFT, padx=5)
        self.setup_slider_bindings(self.detail_slider, 5)  # 添加键盘绑定
        
        # 饱和度滑块
        Label(control_frame, text="饱和度:", **label_style).pack(side=tk.LEFT, padx=10)
        self.saturation_slider = Scale(control_frame, from_=0.0, to=2.0, resolution=0.1,
                                     orient=tk.HORIZONTAL, command=self.schedule_update,
                                     **slider_style)
        self.saturation_slider.set(self.saturation)
        self.saturation_slider.pack(side=tk.LEFT, padx=5)
        self.setup_slider_bindings(self.saturation_slider, 0.1)  # 添加键盘绑定
        
        # 按钮区域
        button_frame = Frame(main_frame, bg=self.bg_color)
        button_frame.pack(fill=tk.X, pady=10)
        
        # 按钮样式
        button_style = {
            "font": ("微软雅黑", 10),
            "bg": self.accent_color,
            "fg": "white",
            "relief": "flat",
            "padx": 20,
            "pady": 5,
            "cursor": "hand2"
        }
        
        Button(button_frame, text="选择图片", command=self.select_image,
               **button_style).pack(side=tk.LEFT, padx=5)
        Button(button_frame, text="应用效果", command=self.apply_effect,
               **button_style).pack(side=tk.LEFT, padx=5)
        Button(button_frame, text="保存像素图", command=self.save_image,
               **button_style).pack(side=tk.LEFT, padx=5)
        
        self.open_folder_btn = Button(button_frame, text="打开保存位置",
                                    command=self.open_save_location,
                                    state=tk.DISABLED, **button_style)
        self.open_folder_btn.pack(side=tk.LEFT, padx=5)
        
        # 进度条样式
        style = ttk.Style()
        style.configure("Custom.Horizontal.TProgressbar",
                       troughcolor='#e0e0e0',
                       background=self.accent_color)
        
        self.progress = ttk.Progressbar(button_frame, length=200,
                                      mode='determinate',
                                      style="Custom.Horizontal.TProgressbar")
        self.progress.pack(side=tk.LEFT, padx=20)
        
        # 添加状态文本标签
        self.status_label = Label(button_frame, text="", font=("微软雅黑", 10),
                                bg=self.bg_color, fg="#666666")
        self.status_label.pack(side=tk.LEFT, padx=5)

    def save_image(self):
        if self.pixelated_image:
            save_path = filedialog.asksaveasfilename(
                defaultextension=".png",
                filetypes=[("PNG", "*.png"), ("JPEG", "*.jpg"), ("BMP", "*.bmp")])
            
            if save_path:
                self.pixelated_image.save(save_path)
                self.last_save_path = save_path  # 记录保存路径
                self.open_folder_btn.config(state=tk.NORMAL)  # 启用打开文件夹按钮
                self.status_label.config(text="保存成功")  # 显示状态文本
                
                # 3秒后清除状态文本
                self.root.after(3000, lambda: self.status_label.config(text=""))

    def setup_slider_bindings(self, slider, step):
        """设置滑块的键盘绑定"""
        # 焦点事件
        slider.bind('<FocusIn>', lambda e: self.on_slider_focus(slider))
        slider.bind('<FocusOut>', lambda e: self.on_slider_focus(None))
        
        # 键盘事件
        slider.bind('<Left>', lambda e: self.adjust_slider(slider, -step))
        slider.bind('<Right>', lambda e: self.adjust_slider(slider, step))
        slider.bind('<Up>', lambda e: self.adjust_slider(slider, step))
        slider.bind('<Down>', lambda e: self.adjust_slider(slider, -step))
        
        # 鼠标点击时获取焦点
        slider.bind('<Button-1>', lambda e: slider.focus_set())

    def on_slider_focus(self, slider):
        """处理滑块焦点变化"""
        self.focused_slider = slider
        
        # 更新滑块外观以显示焦点状态
        for s in [self.pixel_slider, self.detail_slider, self.saturation_slider]:
            if s == slider:
                s.configure(highlightbackground='#0078D7', highlightthickness=2)
            else:
                s.configure(highlightbackground='gray', highlightthickness=1)

    def adjust_slider(self, slider, step):
        """调整滑块值"""
        current = float(slider.get())
        new_value = current + step
        
        # 确保值在有效范围内
        min_val = float(slider.cget('from'))
        max_val = float(slider.cget('to'))
        new_value = max(min_val, min(max_val, new_value))
        
        # 如果是饱和度滑块，保留一位小数
        if slider == self.saturation_slider:
            new_value = round(new_value, 1)
        else:
            new_value = int(new_value)  # 其他滑块使用整数值
        
        # 设置新值并触发更新
        slider.set(new_value)
        self.schedule_update()

    def detect_edges(self, image):
        """检测图像边缘"""
        edges = image.filter(ImageFilter.FIND_EDGES)
        return edges

    def enhance_details(self, image, edge_image, detail_level):
        """增强图像细节"""
        # 将边缘图像转换为numpy数组
        edge_array = np.array(edge_image.convert('L'))
        
        # 使用更敏感的边缘检测阈值
        edge_threshold = 20  # 降低阈值以检测更多细节
        mask = edge_array > edge_threshold
        
        # 创建渐变mask以实现平滑过渡
        mask = mask.astype(np.float32)
        
        # 应用高斯模糊使边缘过渡更自然
        mask = Image.fromarray((mask * 255).astype(np.uint8))
        mask = mask.filter(ImageFilter.GaussianBlur(radius=0.5))
        mask = np.array(mask) / 255.0
        
        # 根据细节级别调整强度
        detail_strength = detail_level / 100.0
        mask = mask * detail_strength
        
        # 扩展mask维度以匹配图像数组
        mask = np.stack([mask] * 3, axis=2)
        
        # 将原图转换为numpy数组
        img_array = np.array(image)
        
        # 创建增强后的图像版本
        enhanced = np.array(image.filter(ImageFilter.EDGE_ENHANCE_MORE))
        
        # 混合原始图像和增强图像
        result = img_array * (1 - mask) + enhanced * mask
        
        return Image.fromarray(result.astype(np.uint8))

    def pixelate_image(self, image, pixel_size):
        if image is None:
            return None
            
        # 获取原始尺寸
        width, height = image.size
        
        # 调整饱和度（保持在颜色处理之前）
        saturation = self.saturation_slider.get()
        if saturation != 1.0:
            enhancer = ImageEnhance.Color(image)
            image = enhancer.enhance(saturation)
        
        # 检测边缘
        edge_image = self.detect_edges(image)
        
        # 缩小图片
        small_width = max(1, int(width // (pixel_size * 0.8)))
        small_height = max(1, int(height // (pixel_size * 0.8)))
        
        # 使用LANCZOS重采样进行缩小，保持颜色更准确
        small_image = image.resize((small_width, small_height), Image.LANCZOS)
        
        # 放大时使用NEAREST确保像素风格
        result = small_image.resize((width, height), Image.NEAREST)
        
        # 增强细节（移到缩放之后）
        detail_level = self.detail_slider.get()
        if detail_level > 0:
            # 先进行像素化
            small_image = image.resize((small_width, small_height), Image.LANCZOS)
            result = small_image.resize((width, height), Image.NEAREST)
            
            # 然后增强细节
            result = self.enhance_details(result, edge_image, detail_level)
        else:
            # 如果不需要增强细节，直接像素化
            small_image = image.resize((small_width, small_height), Image.LANCZOS)
            result = small_image.resize((width, height), Image.NEAREST)
        
        # 最终的颜色调整：使用更温和的量化方法
        result = result.quantize(colors=256, method=2).convert('RGB')
        
        return result
    
    def schedule_update(self, event=None):
        """使用防抖动机制延迟更新"""
        if self.update_timer:
            self.root.after_cancel(self.update_timer)
        self.update_timer = self.root.after(300, self.apply_effect)
    
    def apply_effect(self):
        """应用像素化效果"""
        if not self.original_image:
            return
            
        self.progress['value'] = 0
        self.root.update_idletasks()
        
        self.pixel_size = self.pixel_slider.get()
        self.pixelated_image = self.pixelate_image(self.original_image, self.pixel_size)
        
        self.progress['value'] = 50
        self.root.update_idletasks()
        
        self.display_image(self.pixelated_image, self.pixel_display)
        
        self.progress['value'] = 100
        self.root.update_idletasks()
    
    def select_image(self):
        self.image_path = filedialog.askopenfilename(
            filetypes=[("图片文件", "*.png;*.jpg;*.jpeg;*.bmp;*.gif")])
        
        if self.image_path:
            self.original_image = Image.open(self.image_path)
            self.display_image(self.original_image, self.original_display)
            self.apply_effect()
    
    def display_image(self, image, label):
        if image is None:
            return
            
        # 调整图片大小以适应显示区域
        display_width = 400
        display_height = 350
        
        # 保持宽高比
        width, height = image.size
        ratio = min(display_width/width, display_height/height)
        new_width = int(width * ratio)
        new_height = int(height * ratio)
        
        resized_image = image.resize((new_width, new_height), Image.LANCZOS)
        photo = ImageTk.PhotoImage(resized_image)
        
        label.config(image=photo, width=display_width, height=display_height)
        label.image = photo

    def open_save_location(self):
        """打开保存文件的位置"""
        if not self.last_save_path or not os.path.exists(self.last_save_path):
            messagebox.showinfo("提示", "还没有保存过图片")
            return
            
        # 获取文件所在的文件夹路径
        folder_path = os.path.dirname(self.last_save_path)
        
        # 根据不同操作系统打开文件夹
        try:
            if platform.system() == "Windows":
                os.startfile(folder_path)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", folder_path])
            else:  # Linux
                subprocess.run(["xdg-open", folder_path])
        except Exception as e:
            messagebox.showerror("错误", f"无法打开文件夹：{str(e)}")

    def apply_style(self, style_name):
        """应用预设风格"""
        if style_name not in self.styles:
            return
            
        # 更新当前风格
        self.current_style = style_name
        
        # 更新按钮状态
        for name, btn in self.style_buttons.items():
            if name == style_name:
                btn.configure(bg=self.accent_color, fg="white")
            else:
                btn.configure(bg="#ffffff", fg="black")
        
        # 更新滑块值
        style = self.styles[style_name]
        self.pixel_slider.set(style["pixel"])
        self.detail_slider.set(style["detail"])
        self.saturation_slider.set(style["saturation"])
        
        # 应用效果
        self.apply_effect()

if __name__ == "__main__":
    root = tk.Tk()
    app = PixelArtConverter(root)
    root.mainloop()
