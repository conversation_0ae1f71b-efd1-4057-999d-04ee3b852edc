import sys
import json
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import requests
from bs4 import BeautifulSoup
import time
import os


class Worker(QThread):
    finished = pyqtSignal(str)
    status = pyqtSignal(str)
    progress = pyqtSignal(int)  # 新增进度信号

    def __init__(self, input_text, max_retries=3):
        super().__init__()
        self.input_text = input_text
        self.max_retries = max_retries
        self.session = requests.Session()
        
        # 加载历史记录
        self.history_file = os.path.join(os.path.dirname(__file__), 'search_history.json')
        self.load_history()

    def load_history(self):
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    self.history = json.load(f)
            else:
                self.history = {}
        except:
            self.history = {}

    def save_history(self, query, result):
        self.history[query] = {
            'result': result,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存历史记录失败: {e}")

    def run(self):
        # 检查历史记录
        if self.input_text in self.history:
            self.status.emit("从历史记录中获取...")
            time.sleep(0.5)  # 稍微延迟以显示状态
            self.finished.emit(self.history[self.input_text]['result'])
            return

        retries = 0
        while retries < self.max_retries:
            try:
                self.status.emit("正在查询中...")
                self.progress.emit(30 + retries * 20)  # 进度反馈
                
                url = f"https://www.javbus.com/{self.input_text}"
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9',
                    'Referer': 'https://www.javbus.com'
                }

                response = self.session.get(url, headers=headers, timeout=10)
                self.progress.emit(70)  # 进度反馈
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    container_div = soup.find('div', class_='container')
                    if container_div:
                        star_name_divs = container_div.find_all('div', class_='star-name')
                        if star_name_divs:
                            result = ""
                            for div in star_name_divs:
                                result += f"{div.get_text(strip=True)}\n"
                            result = result.strip()
                            self.progress.emit(100)
                            self.save_history(self.input_text, result)
                            self.finished.emit(result)
                            return
                        else:
                            result = "未找到演员信息"
                    else:
                        result = "页面结构不符"
                else:
                    result = f"请求失败 (状态码: {response.status_code})"
                
                retries += 1
                if retries < self.max_retries:
                    self.status.emit(f"重试第 {retries} 次...")
                    time.sleep(1)
                else:
                    self.progress.emit(0)
                    self.finished.emit(result)

            except Exception as e:
                retries += 1
                if retries < self.max_retries:
                    self.status.emit(f"发生错误，重试第 {retries} 次...")
                    time.sleep(1)
                else:
                    self.progress.emit(0)
                    self.finished.emit(f"查询失败: {str(e)}")


class ModernButton(QPushButton):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.setCursor(Qt.PointingHandCursor)
        self._animation = QPropertyAnimation(self, b"geometry")
        self._animation.setDuration(100)
        # 设置基础样式
        self.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 5px;
                border-radius: 3px;
                min-width: 80px;
                font-family: 'Microsoft YaHei UI';
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        
    def enterEvent(self, event):
        super().enterEvent(event)

    def leaveEvent(self, event):
        super().leaveEvent(event)


class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("演员名称查询")
        self.setWindowIcon(self.create_icon())
        self.setup_ui()
        self.center()
        
        # 加载历史记录作为自动完成
        self.load_search_history()

    def create_icon(self):
        # 创建一个简单的图标
        pixmap = QPixmap(32, 32)
        pixmap.fill(Qt.transparent)
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setBrush(QBrush(QColor("#4CAF50")))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(0, 0, 32, 32)
        painter.setPen(QPen(Qt.white, 2))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, "S")
        painter.end()
        return QIcon(pixmap)

    def setup_ui(self):
        self.setStyleSheet("""
            QWidget {
                font-family: 'Microsoft YaHei UI';
                font-size: 12px;
                background-color: #f5f5f5;
            }
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 5px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
            }
            QTextEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 5px;
                background-color: white;
            }
            QLabel {
                color: #333;
            }
            QProgressBar {
                border: none;
                border-radius: 3px;
                background-color: #ddd;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)

        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(25, 25, 25, 25)

        # 标题
        title_label = QLabel("演员名称查询")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            padding-bottom: 10px;
        """)
        main_layout.addWidget(title_label, alignment=Qt.AlignCenter)

        # 输入区域
        input_container = QWidget()
        input_container.setStyleSheet("""
            QWidget {
                background-color: white;
                border-radius: 8px;
            }
            QLabel {
                padding-left: 2px;
            }
        """)
        input_layout = QVBoxLayout(input_container)
        input_layout.setContentsMargins(15, 15, 15, 15)
        input_layout.setSpacing(10)
        input_layout.setAlignment(Qt.AlignLeft)
        
        input_header = QHBoxLayout()
        input_header.setSpacing(8)  # 设置组件之间的间距
        input_header.setAlignment(Qt.AlignLeft)
        
        input_label = QLabel("番号:")
        input_label.setFixedHeight(35)
        input_label.setFixedWidth(40)  # 固定标签宽度
        input_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        input_label.setStyleSheet("font-weight: bold;")  # 加粗标签文字
        
        self.input_field = QLineEdit()
        self.input_field.setFixedHeight(35)
        self.input_field.setPlaceholderText("请输入要查询的番号...")
        self.input_field.returnPressed.connect(self.search_name)
        self.input_field.setStyleSheet("""
            QLineEdit {
                padding-left: 8px;
                padding-right: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            QLineEdit:focus {
                border: 1px solid #4CAF50;
            }
        """)
        
        search_button = ModernButton("查询")
        search_button.setFixedSize(80, 35)
        search_button.clicked.connect(self.search_name)
        
        input_header.addWidget(input_label)
        input_header.addWidget(self.input_field)
        input_header.addWidget(search_button)
        input_layout.addLayout(input_header)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(3)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setValue(0)
        input_layout.addWidget(self.progress_bar)
        
        main_layout.addWidget(input_container)

        # 状态标签
        self.status_label = QLabel()
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #666;")
        main_layout.addWidget(self.status_label)

        # 结果区域
        result_container = QWidget()
        result_container.setStyleSheet("""
            QWidget {
                background-color: white;
                border-radius: 8px;
            }
            QLabel {
                color: #2c3e50;
            }
        """)
        result_layout = QVBoxLayout(result_container)
        result_layout.setContentsMargins(15, 12, 15, 12)
        result_layout.setSpacing(8)
        
        result_header = QLabel("查询结果")
        result_header.setStyleSheet("font-weight: bold; color: #2c3e50;")
        result_layout.addWidget(result_header)
        
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setFixedHeight(80)  # 减小高度
        self.result_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ddd;
                padding: 8px;
                background-color: #fafafa;
                border-radius: 4px;
            }
        """)
        result_layout.addWidget(self.result_text)

        # 底部按钮区域
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(8)
        buttons_layout.addStretch()
        
        clear_button = ModernButton("清除")
        clear_button.setFixedSize(80, 30)  # 稍微减小按钮高度
        clear_button.clicked.connect(self.clear_result)
        
        copy_button = ModernButton("复制结果")
        copy_button.setFixedSize(80, 30)  # 稍微减小按钮高度
        copy_button.clicked.connect(self.copy_result)
        
        buttons_layout.addWidget(clear_button)
        buttons_layout.addWidget(copy_button)
        result_layout.addLayout(buttons_layout)
        
        main_layout.addWidget(result_container)

        # 设置窗口大小
        self.setFixedSize(500, 400)  # 减小窗口高度

    def load_search_history(self):
        history_file = os.path.join(os.path.dirname(__file__), 'search_history.json')
        if os.path.exists(history_file):
            try:
                with open(history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
                completer = QCompleter(list(history.keys()))
                completer.setCaseSensitivity(Qt.CaseInsensitive)
                self.input_field.setCompleter(completer)
            except:
                pass

    def center(self):
        qr = self.frameGeometry()
        cp = QScreen.availableGeometry(QApplication.primaryScreen()).center()
        qr.moveCenter(cp)
        self.move(qr.topLeft())

    def search_name(self):
        name = self.input_field.text().strip()
        if not name:
            QMessageBox.warning(self, "提示", "请输入番号！")
            return

        self.result_text.clear()
        self.status_label.setText("正在查询...")
        self.progress_bar.setValue(10)
        
        self.worker = Worker(name)
        self.worker.finished.connect(self.update_result)
        self.worker.status.connect(self.update_status)
        self.worker.progress.connect(self.progress_bar.setValue)
        self.worker.start()

    def update_result(self, result):
        self.result_text.setPlainText(result)
        self.status_label.clear()
        self.progress_bar.setValue(0)
        # 更新自动完成器
        self.load_search_history()

    def update_status(self, status):
        self.status_label.setText(status)

    def copy_result(self):
        text = self.result_text.toPlainText()
        if text:
            clipboard = QApplication.clipboard()
            clipboard.setText(text)
            self.status_label.setText("已复制到剪贴板！")
            QTimer.singleShot(2000, lambda: self.status_label.clear())
        else:
            QMessageBox.warning(self, "提示", "没有可复制的内容！")

    def clear_result(self):
        self.result_text.clear()
        self.input_field.clear()
        self.status_label.clear()
        self.progress_bar.setValue(0)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 设置应用程序级别的样式
    app.setStyle("Fusion")
    
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())