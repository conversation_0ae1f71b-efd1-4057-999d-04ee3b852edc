#!/usr/bin/env python3
"""
测试脚本：验证字节转换和时间戳展示的改进
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from converter import ConverterApp
import tkinter as tk
import time

def test_byte_formatting():
    """测试字节格式化功能"""
    print("=== 测试字节格式化功能 ===")
    
    # 创建一个临时的ConverterApp实例来测试格式化方法
    root = tk.Tk()
    root.withdraw()  # 隐藏窗口
    app = ConverterApp(root)
    
    test_values = [
        0,
        1,
        1024,
        1048576,
        1073741824,
        1.5,
        0.001,
        1234567.89,
        1e15,
        1e-8
    ]
    
    print("测试值 -> 格式化结果")
    print("-" * 30)
    for value in test_values:
        formatted = app.format_bytes_value(value)
        print(f"{value:>12} -> {formatted}")
        assert isinstance(formatted, str)
    
    root.destroy()
    print("字节格式化测试完成！\n")

def test_byte_formatting_edge_cases():
    """测试字节格式化的异常和边界情况"""
    print("=== 字节格式化边界与异常测试 ===")
    root = tk.Tk()
    root.withdraw()
    app = ConverterApp(root)
    # None
    try:
        app.format_bytes_value(None)
        assert False, "None 应抛出异常"
    except Exception:
        pass
    # 非数字字符串
    try:
        app.format_bytes_value("abc")
        assert False, "字符串应抛出异常"
    except Exception:
        pass
    # 负数
    formatted = app.format_bytes_value(-1024)
    assert isinstance(formatted, str)
    # 极大/极小数
    assert "e" in app.format_bytes_value(1e20)
    assert "e" in app.format_bytes_value(1e-20)
    # 溢出数值
    try:
        app.format_bytes_value(float('inf'))
    except Exception:
        pass
    root.destroy()
    print("字节格式化边界与异常测试完成！\n")

def test_relative_time():
    """测试相对时间功能"""
    print("=== 测试相对时间功能 ===")
    
    # 创建一个临时的ConverterApp实例来测试相对时间方法
    root = tk.Tk()
    root.withdraw()  # 隐藏窗口
    app = ConverterApp(root)
    
    current_time = time.time()
    test_timestamps = [
        current_time,  # 现在
        current_time - 30,  # 30秒前
        current_time - 300,  # 5分钟前
        current_time - 3600,  # 1小时前
        current_time - 86400,  # 1天前
        current_time - 2592000,  # 30天前
        current_time - 31536000,  # 1年前
        current_time + 3600,  # 1小时后
    ]
    
    print("时间戳 -> 相对时间")
    print("-" * 40)
    for timestamp in test_timestamps:
        relative = app.get_relative_time(timestamp)
        print(f"{timestamp:.0f} -> {relative}")
        assert isinstance(relative, str)
    
    root.destroy()
    print("相对时间测试完成！\n")

def test_relative_time_edge_cases():
    """测试相对时间的异常和边界情况"""
    print("=== 相对时间边界与异常测试 ===")
    root = tk.Tk()
    root.withdraw()
    app = ConverterApp(root)
    # None
    assert app.get_relative_time(None) == "无法计算"
    # 非数字
    assert app.get_relative_time("abc") == "无法计算"
    # 极大时间戳（未来）
    future = time.time() + 1e10
    assert "后" in app.get_relative_time(future)
    # 极小时间戳（过去）
    past = time.time() - 1e10
    assert "前" in app.get_relative_time(past)
    root.destroy()
    print("相对时间边界与异常测试完成！\n")

def demo_improvements():
    """演示改进功能"""
    print("=== 改进功能演示 ===")
    print("1. 字节转换现在使用更清晰的数字格式")
    print("2. 时间戳转换现在显示更多信息：")
    print("   - 主要结果（标准格式）")
    print("   - 详细信息（中文格式 + 星期）")
    print("   - ISO格式")
    print("   - 相对时间")
    print("\n请运行主程序查看实际效果！")

if __name__ == "__main__":
    print("字节转换和时间戳展示改进测试")
    print("=" * 50)
    
    test_byte_formatting()
    test_byte_formatting_edge_cases()
    test_relative_time()
    test_relative_time_edge_cases()
    demo_improvements()
    
    print("所有测试完成！")
