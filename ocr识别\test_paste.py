#!/usr/bin/env python3
"""
测试剪贴板粘贴功能的简单脚本
"""

import sys
from PIL import Image, ImageGrab
import io

def test_clipboard():
    """测试剪贴板中是否有图片"""
    print("正在检查剪贴板...")

    try:
        image = ImageGrab.grabclipboard()
        print(f"剪贴板内容类型: {type(image)}")

        if isinstance(image, Image.Image):
            print("找到图片！")
            print(f"图片尺寸: {image.size}")
            print(f"图片模式: {image.mode}")
            print(f"图片格式: {image.format}")

            # 尝试转换为字节数据
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format='PNG')
            image_data = img_byte_arr.getvalue()
            print(f"转换后的数据大小: {len(image_data)} 字节")

            return True
        else:
            print("剪贴板中没有图片")
            print(f"剪贴板内容: {image}")
            return False

    except Exception as e:
        print(f"检查剪贴板时出错: {e}")
        return False

if __name__ == "__main__":
    print("=== 剪贴板图片检测工具 ===")
    print("请先复制一张图片到剪贴板，然后运行此脚本")
    print()

    result = test_clipboard()

    if result:
        print("\n剪贴板功能正常，图片可以被正确读取")
    else:
        print("\n剪贴板中没有图片或读取失败")
        print("请确保：")
        print("1. 已经复制了一张图片到剪贴板")
        print("2. 图片格式被支持（PNG、JPG、BMP等）")
        print("3. 没有其他程序占用剪贴板")
