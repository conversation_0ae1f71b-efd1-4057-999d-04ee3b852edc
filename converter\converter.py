import tkinter as tk
from tkinter import ttk, font
import time
import datetime
import requests  # 添加requests库，用于获取实时汇率

# 尝试导入BeautifulSoup，如果不可用，记录错误
try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False

# 自定义圆角框架类
class RoundedFrame(tk.Canvas):
    """A canvas widget with rounded corners and optional shadow effect"""
    def __init__(self, parent, width=0, height=0, cornerradius=10, padding=0, bg=None, fg=None,
                 shadow_color=None, shadow_size=5, **kwargs):
        super().__init__(parent, **kwargs)

        self.background = bg if bg else parent["background"]
        self.cornerradius = cornerradius
        self.padding = padding
        self.shadow_color = shadow_color
        self.shadow_size = shadow_size if shadow_color else 0

        # 始终初始化宽度和高度属性
        self.width = width if width else 100  # 默认初始宽度
        self.height = height if height else 100  # 默认初始高度

        self.bind("<Configure>", self._on_configure)

    def _on_configure(self, event):
        self.width = event.width
        self.height = event.height
        self.delete("all")

        # 确保初始宽度和高度
        if not hasattr(self, 'width') or not self.width:
            self.width = event.width
        if not hasattr(self, 'height') or not self.height:
            self.height = event.height

        # Draw shadow if needed
        if self.shadow_color:
            shadow_width = self.width - self.shadow_size
            shadow_height = self.height - self.shadow_size
            self.create_rounded_rect(
                self.shadow_size, self.shadow_size,
                shadow_width + self.shadow_size, shadow_height + self.shadow_size,
                self.cornerradius, fill=self.shadow_color, outline=""
            )

        # Draw main rectangle
        self.create_rounded_rect(
            0, 0,
            self.width - self.shadow_size, self.height - self.shadow_size,
            self.cornerradius, fill=self.background, outline=""
        )

    def create_rounded_rect(self, x1, y1, x2, y2, r, **kwargs):
        """Create a rounded rectangle"""
        points = [
            x1+r, y1,
            x2-r, y1,
            x2, y1,
            x2, y1+r,
            x2, y2-r,
            x2, y2,
            x2-r, y2,
            x1+r, y2,
            x1, y2,
            x1, y2-r,
            x1, y1+r,
            x1, y1
        ]
        return self.create_polygon(points, smooth=True, **kwargs)

    def add_widget(self, widget):
        """Add a widget to the canvas with padding"""
        # 确保update_idletasks在widget放置前被调用，以获取正确的宽度和高度
        self.update_idletasks()

        # 安全地获取宽度和高度值
        widget_width = max(10, self.width - (2 * self.padding) - self.shadow_size) if hasattr(self, 'width') else 100
        widget_height = max(10, self.height - (2 * self.padding) - self.shadow_size) if hasattr(self, 'height') else 100

        widget.place(
            x=self.padding + (self.shadow_size if self.shadow_color else 0),
            y=self.padding + (self.shadow_size if self.shadow_color else 0),
            width=widget_width,
            height=widget_height,
            relwidth=1 if widget_width <= 10 else 0,
            relheight=1 if widget_height <= 10 else 0
        )

class ConverterApp:
    """
    A beautiful application for unit conversions.
    Supports:
    - Byte units (B, KB, MB, GB, TB, PB)
    - Timestamp conversion
    """

    def __init__(self, root):
        self.root = root
        self.root.title("单位转换工具")
        self.root.geometry("700x600")
        self.root.resizable(True, True)
        self.root.minsize(650, 550)

        # 设置现代主题色
        self.primary_color = "#4361ee"      # 主色调（蓝色）
        self.secondary_color = "#f8f9fa"    # 次要色（浅灰色）
        self.accent_color = "#4cc9f0"       # 强调色（青色）
        self.success_color = "#4fc988"      # 成功色（绿色）

        # 文本颜色
        self.text_color = "#2b2d42"         # 主要文本颜色（深色）
        self.light_text = "#8d99ae"         # 次要文本颜色（灰色）

        # 背景颜色
        self.bg_color = "#f5f7fa"           # 主要背景颜色（浅灰）
        self.card_bg = "#ffffff"            # 卡片背景颜色（白色）
        self.border_color = "#e9ecef"       # 边框颜色（浅灰）

        # 阴影颜色
        self.shadow_color = "#dddddd"       # 阴影颜色

        # 按钮悬停颜色
        self.hover_color = "#3b50ce"        # 按钮悬停颜色

        # 设置样式
        self.setup_styles()

        # Unit conversion factors (powers of 1024)
        self.units = {
            "字节 (B)": 0,
            "千字节 (KB)": 1,
            "兆字节 (MB)": 2,
            "千兆字节 (GB)": 3,
            "太字节 (TB)": 4,
            "拍字节 (PB)": 5
        }
        
        # 初始化汇率相关变量
        self.exchange_rate = 7.2  # 默认汇率：1美元 = 7.2人民币
        self.last_rate_update = None  # 上次汇率更新时间

        # 创建主界面
        self.setup_ui()

        # 设置窗口图标
        self.set_window_icon()

    def set_window_icon(self):
        """设置窗口图标"""
        try:
            self.root.iconbitmap("icon.ico")
        except:
            # 如果找不到图标文件，使用默认图标
            pass

    def setup_styles(self):
        """设置自定义样式"""
        # 配置字体
        default_font = font.nametofont("TkDefaultFont")
        default_font.configure(family="Microsoft YaHei UI", size=10)
        self.root.option_add("*Font", default_font)

        # 创建样式对象
        style = ttk.Style()

        # 使用默认主题
        try:
            style.theme_use("default")
        except:
            pass

        # 配置通用样式
        style.configure(".", background=self.bg_color)

        # 框架样式
        style.configure("TFrame", background=self.bg_color)

        # 标签样式
        style.configure("TLabel",
                      font=("Microsoft YaHei UI", 10),
                      background=self.bg_color,
                      foreground=self.text_color)

        # 状态栏样式
        style.configure("Status.TLabel",
                      font=("Microsoft YaHei UI", 9),
                      foreground=self.light_text,
                      background=self.bg_color)

        # 标题样式
        style.configure("Title.TLabel",
                      font=("Microsoft YaHei UI", 18, "bold"),
                      foreground=self.text_color,
                      background=self.bg_color)

        # 结果标签样式
        style.configure("Result.TLabel",
                      font=("Microsoft YaHei UI", 12),
                      foreground=self.text_color,
                      background=self.bg_color)

        # 单位标签样式
        style.configure("Unit.TLabel",
                      font=("Microsoft YaHei UI", 11, "bold"),
                      foreground=self.text_color,
                      background=self.bg_color)

        # 主按钮样式
        style.configure("TButton",
                      font=("Microsoft YaHei UI", 10, "bold"),
                      padding=(15, 8))

        # 按钮悬停效果
        style.map("TButton",
                foreground=[("active", "white"), ("!active", "white")],
                background=[("active", self.hover_color), ("!active", self.primary_color)])

        # 输入框样式
        style.configure("TEntry",
                      font=("Microsoft YaHei UI", 10),
                      fieldbackground="white",
                      borderwidth=1)

        # 输入框焦点效果
        style.map("TEntry",
                bordercolor=[("focus", self.primary_color), ("!focus", self.border_color)])

        # 下拉框样式
        style.configure("TCombobox",
                      font=("Microsoft YaHei UI", 10),
                      fieldbackground="white")

        # 标签框样式
        style.configure("TLabelframe",
                      font=("Microsoft YaHei UI", 10),
                      background=self.bg_color)

        style.configure("TLabelframe.Label",
                     font=("Microsoft YaHei UI", 11, "bold"),
                     foreground=self.primary_color,
                     background=self.bg_color)

        # 选项卡样式
        style.configure("TNotebook",
                      background=self.bg_color)

        style.configure("TNotebook.Tab",
                     font=("Microsoft YaHei UI", 10),
                     padding=(15, 5))

        style.map("TNotebook.Tab",
                background=[("selected", self.primary_color), ("!selected", self.secondary_color)],
                foreground=[("selected", "white"), ("!selected", self.text_color)],
                font=[("selected", ("Microsoft YaHei UI", 10, "bold")), ("!selected", ("Microsoft YaHei UI", 10))])


    def setup_ui(self):
        """Set up the user interface with a simple and clean design"""
        # 设置根窗口背景色
        self.root.configure(bg=self.bg_color)

        # 创建主容器
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建选项卡
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # 创建字节转换选项卡
        self.byte_frame = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(self.byte_frame, text="字节单位转换")

        # 创建时间戳转换选项卡
        self.timestamp_frame = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(self.timestamp_frame, text="时间戳转换")
        
        # 创建货币转换选项卡
        self.currency_frame = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(self.currency_frame, text="货币汇率转换")

        # 设置字节转换选项卡的UI
        self.setup_byte_converter_ui()

        # 设置时间戳转换选项卡的UI
        self.setup_timestamp_converter_ui()
        
        # 设置货币转换选项卡的UI
        self.setup_currency_converter_ui()

        # 状态栏
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)

        self.status_var = tk.StringVar(value="准备就绪")
        status_label = ttk.Label(
            status_frame,
            textvariable=self.status_var,
            style="Status.TLabel"
        )
        status_label.pack(side=tk.LEFT, padx=10, pady=5)

    def setup_byte_converter_ui(self):
        """Setup UI for byte unit conversion (MCP美化)"""
        # 输入框架
        input_frame = ttk.Frame(self.byte_frame)
        input_frame.pack(fill=tk.X, pady=10)

        ttk.Label(input_frame, text="输入数值:").pack(side=tk.LEFT, padx=(0, 10))
        self.byte_value_var = tk.StringVar()
        self.byte_entry = ttk.Entry(input_frame, textvariable=self.byte_value_var, width=20)
        self.byte_entry.pack(side=tk.LEFT, padx=5)

        ttk.Label(input_frame, text="单位:").pack(side=tk.LEFT, padx=(20, 10))
        self.byte_unit_var = tk.StringVar()
        byte_unit_combo = ttk.Combobox(input_frame, textvariable=self.byte_unit_var, values=list(self.units.keys()), width=15, state="readonly")
        byte_unit_combo.current(0)
        byte_unit_combo.pack(side=tk.LEFT, padx=5)

        button_container = ttk.Frame(self.byte_frame)
        button_container.pack(pady=15, fill=tk.X)
        convert_button = ttk.Button(button_container, text="转换", command=self.convert_bytes, width=15)
        button_container.update_idletasks()
        convert_button.pack(pady=5, anchor=tk.CENTER)

        # MCP风格美化结果区
        results_container = ttk.Frame(self.byte_frame)
        results_container.pack(fill=tk.BOTH, expand=True, pady=10)
        results_title = ttk.Label(results_container, text="转换结果", style="Title.TLabel")
        results_title.pack(anchor=tk.W, pady=(0, 10))

        # MCP卡片式分组
        card_frame = ttk.Frame(results_container, style="TFrame")
        card_frame.pack(fill=tk.BOTH, expand=True)
        self.byte_result_vars = {}
        self.byte_result_labels = {}
        row, col = 0, 0
        for unit in self.units.keys():
            # 统一色块卡片
            card = tk.Frame(card_frame, bg="#f8f9fa", bd=0, highlightthickness=0)
            card.grid(row=row, column=col, sticky="nsew", padx=18, pady=12, ipadx=8, ipady=8)
            # 统一样式，不再高亮
            unit_label = tk.Label(card, text=unit, font=("Microsoft YaHei UI", 11, "bold"), fg=self.text_color, bg=card["bg"])
            unit_label.pack(anchor="w")
            self.byte_result_vars[unit] = tk.StringVar(value="0")
            result_label = tk.Label(card, textvariable=self.byte_result_vars[unit], font=("Microsoft YaHei UI", 14, "normal"), fg=self.text_color, bg=card["bg"])
            result_label.pack(anchor="w", pady=(8, 0))
            self.byte_result_labels[unit] = result_label
            col += 1
            if col > 1:
                col = 0
                row += 1
        for i in range(2):
            card_frame.columnconfigure(i, weight=1)
        for i in range(3):
            card_frame.rowconfigure(i, weight=1)
        self.byte_entry.bind("<Return>", lambda e: self.convert_bytes())
        self.byte_entry.bind("<KeyRelease>", lambda e: self.validate_byte_input())
        byte_unit_combo.bind("<<ComboboxSelected>>", lambda e: self.convert_bytes())

    def setup_timestamp_converter_ui(self):
        """Setup UI for timestamp conversion (MCP美化)"""
        input_frame = ttk.Frame(self.timestamp_frame)
        input_frame.pack(fill=tk.X, pady=10)
        ttk.Label(input_frame, text="输入时间戳/时间:").pack(side=tk.LEFT, padx=(0, 10))
        self.timestamp_value_var = tk.StringVar()
        self.timestamp_entry = ttk.Entry(input_frame, textvariable=self.timestamp_value_var, width=25)
        self.timestamp_entry.pack(side=tk.LEFT, padx=5)
        ttk.Label(input_frame, text="转换类型:").pack(side=tk.LEFT, padx=(20, 10))
        self.timestamp_type_var = tk.StringVar()
        timestamp_type_combo = ttk.Combobox(input_frame, textvariable=self.timestamp_type_var, values=["时间戳转时间", "时间转时间戳"], width=15, state="readonly")
        timestamp_type_combo.current(0)
        timestamp_type_combo.pack(side=tk.LEFT, padx=5)
        button_container = ttk.Frame(self.timestamp_frame)
        button_container.pack(fill=tk.X, pady=15)
        button_frame = ttk.Frame(button_container)
        button_frame.pack(anchor=tk.CENTER)
        now_button = ttk.Button(button_frame, text="当前时间", command=self.set_current_time, width=12)
        now_button.pack(side=tk.LEFT, padx=(0, 15))
        convert_button = ttk.Button(button_frame, text="转换", command=self.convert_timestamp, width=12)
        convert_button.pack(side=tk.LEFT)
        results_container = ttk.Frame(self.timestamp_frame)
        results_container.pack(fill=tk.BOTH, expand=True, pady=10)
        results_title = ttk.Label(results_container, text="转换结果", style="Title.TLabel")
        results_title.pack(anchor=tk.W, pady=(0, 10))
        # MCP分组卡片美化
        card_frame = ttk.Frame(results_container, style="TFrame")
        card_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.timestamp_results = {}
        # 主结果卡片
        main_card = tk.Frame(card_frame, bg="#f8f9fa", bd=0, highlightthickness=0)
        main_card.pack(fill=tk.X, pady=(0, 18))
        main_title = tk.Label(main_card, text="主要结果", font=("Microsoft YaHei UI", 12, "bold"), fg=self.text_color, bg=main_card["bg"])
        main_title.pack(anchor="w", padx=18, pady=(10, 0))
        self.timestamp_results['main'] = tk.StringVar(value="")
        main_result = tk.Label(main_card, textvariable=self.timestamp_results['main'], font=("Microsoft YaHei UI", 18, "bold"), fg=self.text_color, bg=main_card["bg"], wraplength=600)
        main_result.pack(anchor="w", padx=18, pady=(8, 16))
        # 分割线
        sep1 = tk.Frame(card_frame, height=2, bg="#e9ecef")
        sep1.pack(fill=tk.X, pady=(0, 8))
        # 详细信息卡片
        detail_card = tk.Frame(card_frame, bg="#f8f9fa", bd=0, highlightthickness=0)
        detail_card.pack(fill=tk.X, pady=(0, 12))
        detail_title = tk.Label(detail_card, text="详细信息", font=("Microsoft YaHei UI", 11, "bold"), fg=self.text_color, bg=detail_card["bg"])
        detail_title.pack(anchor="w", padx=18, pady=(10, 0))
        self.timestamp_results['detail'] = tk.StringVar(value="")
        detail_result = tk.Label(detail_card, textvariable=self.timestamp_results['detail'], font=("Microsoft YaHei UI", 14), fg=self.text_color, bg=detail_card["bg"], wraplength=600)
        detail_result.pack(anchor="w", padx=18, pady=(8, 10))
        # 分割线
        sep2 = tk.Frame(card_frame, height=2, bg="#e9ecef")
        sep2.pack(fill=tk.X, pady=(0, 8))
        # ISO格式卡片
        iso_card = tk.Frame(card_frame, bg="#f8f9fa", bd=0, highlightthickness=0)
        iso_card.pack(fill=tk.X, pady=(0, 12))
        iso_title = tk.Label(iso_card, text="ISO格式", font=("Microsoft YaHei UI", 11, "bold"), fg=self.text_color, bg=iso_card["bg"])
        iso_title.pack(anchor="w", padx=18, pady=(10, 0))
        self.timestamp_results['iso'] = tk.StringVar(value="")
        iso_result = tk.Label(iso_card, textvariable=self.timestamp_results['iso'], font=("Microsoft YaHei UI", 13), fg=self.text_color, bg=iso_card["bg"], wraplength=600)
        iso_result.pack(anchor="w", padx=18, pady=(8, 10))
        # 分割线
        sep3 = tk.Frame(card_frame, height=2, bg="#e9ecef")
        sep3.pack(fill=tk.X, pady=(0, 8))
        # 相对时间卡片
        rel_card = tk.Frame(card_frame, bg="#f8f9fa", bd=0, highlightthickness=0)
        rel_card.pack(fill=tk.X, pady=(0, 12))
        rel_title = tk.Label(rel_card, text="相对时间", font=("Microsoft YaHei UI", 11, "bold"), fg=self.text_color, bg=rel_card["bg"])
        rel_title.pack(anchor="w", padx=18, pady=(10, 0))
        self.timestamp_results['relative'] = tk.StringVar(value="")
        rel_result = tk.Label(rel_card, textvariable=self.timestamp_results['relative'], font=("Microsoft YaHei UI", 13), fg=self.text_color, bg=rel_card["bg"], wraplength=600)
        rel_result.pack(anchor="w", padx=18, pady=(8, 10))
        self.timestamp_entry.bind("<Return>", lambda e: self.convert_timestamp())
        self.timestamp_entry.bind("<KeyRelease>", lambda e: self.validate_timestamp_input())
        timestamp_type_combo.bind("<<ComboboxSelected>>", lambda e: self.update_timestamp_placeholder())
        self.update_timestamp_placeholder()

    def validate_byte_input(self):
        """Validate that the input is a valid number for byte conversion"""
        value = self.byte_value_var.get().strip()
        if not value:
            return

        try:
            # Allow for decimal numbers
            float(value)
            self.status_var.set("准备就绪")
        except ValueError:
            self.status_var.set("请输入有效的数字")

    def validate_timestamp_input(self):
        """Validate that the input is a valid timestamp"""
        value = self.timestamp_value_var.get().strip()
        if not value:
            return

        try:
            # Allow for integer or decimal numbers
            float(value)
            self.status_var.set("准备就绪")
        except ValueError:
            self.status_var.set("请输入有效的时间戳")

    def format_bytes_value(self, value):
        """Format byte values for better display"""
        if value == 0:
            return "0"

        # 对于非常大的数字使用科学计数法
        if abs(value) >= 1e15:
            return f"{value:.2e}"

        # 对于非常小的数字使用科学计数法
        if abs(value) < 1e-6 and value != 0:
            return f"{value:.2e}"

        # 对于整数，直接显示
        if value == int(value):
            return f"{int(value):,}"

        # 对于小数，根据大小确定精度
        if abs(value) >= 1000:
            return f"{value:,.2f}"
        elif abs(value) >= 1:
            return f"{value:.4f}"
        else:
            return f"{value:.6f}".rstrip('0').rstrip('.')

    def get_relative_time(self, timestamp):
        """Get relative time description"""
        try:
            now = time.time()
            diff = now - timestamp

            if abs(diff) < 60:
                return "刚刚"
            elif abs(diff) < 3600:
                minutes = int(abs(diff) / 60)
                return f"{minutes}分钟{'前' if diff > 0 else '后'}"
            elif abs(diff) < 86400:
                hours = int(abs(diff) / 3600)
                return f"{hours}小时{'前' if diff > 0 else '后'}"
            elif abs(diff) < 2592000:  # 30 days
                days = int(abs(diff) / 86400)
                return f"{days}天{'前' if diff > 0 else '后'}"
            elif abs(diff) < 31536000:  # 365 days
                months = int(abs(diff) / 2592000)
                return f"{months}个月{'前' if diff > 0 else '后'}"
            else:
                years = int(abs(diff) / 31536000)
                return f"{years}年{'前' if diff > 0 else '后'}"
        except:
            return "无法计算"

    def convert_bytes(self):
        """Convert the input value to all byte units"""
        value = self.byte_value_var.get().strip()
        if not value:
            return

        try:
            # Get the numeric value and source unit
            numeric_value = float(value)
            source_unit = self.byte_unit_var.get()
            source_power = self.units[source_unit]

            # Convert to bytes first (base unit)
            bytes_value = numeric_value * (1024 ** source_power)

            # Convert to all units and update the UI
            for unit, power in self.units.items():
                result = bytes_value / (1024 ** power)

                # 使用新的格式化方法
                formatted_result = self.format_bytes_value(result)

                # 更新结果标签
                self.byte_result_vars[unit].set(formatted_result)

            # 强制更新显示
            self.root.update_idletasks()

            self.status_var.set(f"已将 {self.format_bytes_value(numeric_value)} {source_unit} 转换为所有单位")

        except ValueError:
            self.status_var.set("请输入有效的数字")
        except Exception as e:
            self.status_var.set(f"转换错误: {str(e)}")

    def convert_timestamp(self):
        """Convert the timestamp to human-readable date and time or vice versa"""
        value = self.timestamp_value_var.get().strip()
        if not value:
            return

        try:
            # 获取转换类型
            conversion_type = self.timestamp_type_var.get()

            if conversion_type == "时间戳转时间":
                # 时间戳转时间
                timestamp_value = float(value)

                # 转换为datetime对象
                dt = datetime.datetime.fromtimestamp(timestamp_value)

                # 主要结果 - 标准格式
                main_result = dt.strftime("%Y-%m-%d %H:%M:%S")

                # 详细信息 - 中文格式和星期
                weekdays = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"]
                weekday = weekdays[dt.weekday()]
                detail_result = f"{dt.year}年{dt.month:02d}月{dt.day:02d}日 {weekday} {dt.hour:02d}时{dt.minute:02d}分{dt.second:02d}秒"

                # ISO格式
                iso_result = dt.isoformat()

                # 相对时间
                relative_result = self.get_relative_time(timestamp_value)

                # 更新状态栏
                self.status_var.set(f"已将时间戳 {value} 转换为日期时间")

            else:
                # 时间转时间戳
                try:
                    # 尝试解析时间字符串
                    dt = datetime.datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    try:
                        # 尝试其他常见格式
                        dt = datetime.datetime.strptime(value, "%Y/%m/%d %H:%M:%S")
                    except ValueError:
                        try:
                            dt = datetime.datetime.strptime(value, "%Y-%m-%d")
                        except ValueError:
                            raise ValueError("无法解析时间格式，请使用 YYYY-MM-DD HH:MM:SS 格式")

                # 获取时间戳
                timestamp = dt.timestamp()

                # 主要结果 - Unix时间戳
                main_result = f"Unix时间戳: {int(timestamp)} 秒"

                # 详细信息 - 毫秒时间戳
                detail_result = f"毫秒时间戳: {int(timestamp * 1000)} 毫秒"

                # ISO格式
                iso_result = dt.isoformat()

                # 相对时间
                relative_result = self.get_relative_time(timestamp)

                # 更新状态栏
                self.status_var.set(f"已将日期时间 {value} 转换为时间戳")

            # 更新所有结果
            self.timestamp_results['main'].set(main_result)
            self.timestamp_results['detail'].set(detail_result)
            self.timestamp_results['iso'].set(iso_result)
            self.timestamp_results['relative'].set(relative_result)

            # 强制更新显示
            self.root.update_idletasks()

        except ValueError as e:
            if "无法解析时间格式" in str(e):
                self.status_var.set(str(e))
            else:
                self.status_var.set("请输入有效的时间戳或时间格式")
        except Exception as e:
            self.status_var.set(f"转换错误: {str(e)}")

    def update_timestamp_placeholder(self):
        """Update the placeholder text based on the selected conversion type"""
        conversion_type = self.timestamp_type_var.get()
        if conversion_type == "时间戳转时间":
            # 清除当前输入
            self.timestamp_entry.delete(0, tk.END)
            # 清除所有结果
            for key in self.timestamp_results:
                self.timestamp_results[key].set("")
            # 设置状态栏文本
            if hasattr(self, 'status_var'):
                self.status_var.set("请输入Unix时间戳值，例如: 1609459200")
        else:
            # 清除当前输入
            self.timestamp_entry.delete(0, tk.END)
            # 清除所有结果
            for key in self.timestamp_results:
                self.timestamp_results[key].set("")
            # 设置状态栏文本
            if hasattr(self, 'status_var'):
                self.status_var.set("请输入时间，例如: 2021-01-01 00:00:00")

    def set_current_time(self):
        """Set current time or timestamp based on the conversion type"""
        # 获取转换类型
        conversion_type = self.timestamp_type_var.get()

        if conversion_type == "时间转时间戳":
            # 如果是时间转时间戳，设置当前时间
            now = datetime.datetime.now()
            self.timestamp_value_var.set(now.strftime("%Y-%m-%d %H:%M:%S"))
        else:
            # 如果是时间戳转时间，设置当前时间戳
            current_timestamp = time.time()
            self.timestamp_value_var.set(str(int(current_timestamp)))

        # 转换
        self.convert_timestamp()

    def setup_currency_converter_ui(self):
        """设置货币转换UI（美元转人民币）"""
        # 输入框架
        input_frame = ttk.Frame(self.currency_frame)
        input_frame.pack(fill=tk.X, pady=10)

        ttk.Label(input_frame, text="输入美元金额:").pack(side=tk.LEFT, padx=(0, 10))
        self.usd_value_var = tk.StringVar()
        self.usd_entry = ttk.Entry(input_frame, textvariable=self.usd_value_var, width=20)
        self.usd_entry.pack(side=tk.LEFT, padx=5)

        # 汇率显示与输入区域
        rate_frame = ttk.Frame(self.currency_frame)
        rate_frame.pack(fill=tk.X, pady=10)

        ttk.Label(rate_frame, text="当前汇率:").pack(side=tk.LEFT, padx=(0, 10))
        self.rate_var = tk.StringVar(value=str(self.exchange_rate))
        rate_entry = ttk.Entry(rate_frame, textvariable=self.rate_var, width=10)
        rate_entry.pack(side=tk.LEFT, padx=5)
        ttk.Label(rate_frame, text="人民币/美元").pack(side=tk.LEFT, padx=5)

        # 上次更新时间显示
        self.update_time_var = tk.StringVar(value="尚未更新汇率")
        update_time_label = ttk.Label(rate_frame, textvariable=self.update_time_var, style="Status.TLabel")
        update_time_label.pack(side=tk.RIGHT, padx=10)

        # 按钮区域
        button_frame = ttk.Frame(self.currency_frame)
        button_frame.pack(pady=15, fill=tk.X)

        button_container = ttk.Frame(button_frame)
        button_container.pack(anchor=tk.CENTER)

        update_rate_button = ttk.Button(button_container, text="更新汇率", command=self.get_exchange_rate, width=12)
        update_rate_button.pack(side=tk.LEFT, padx=(0, 15))

        convert_button = ttk.Button(button_container, text="转换", command=self.convert_currency, width=12)
        convert_button.pack(side=tk.LEFT)

        # 结果区域
        results_container = ttk.Frame(self.currency_frame)
        results_container.pack(fill=tk.BOTH, expand=True, pady=10)
        
        results_title = ttk.Label(results_container, text="转换结果", style="Title.TLabel")
        results_title.pack(anchor=tk.W, pady=(0, 10))

        # 结果卡片
        result_card = tk.Frame(results_container, bg="#f8f9fa", bd=0, highlightthickness=0)
        result_card.pack(fill=tk.X, pady=(0, 18), padx=10)
        
        # 美元结果显示
        usd_frame = tk.Frame(result_card, bg=result_card["bg"])
        usd_frame.pack(fill=tk.X, pady=10, padx=18)
        
        usd_title = tk.Label(usd_frame, text="美元金额", font=("Microsoft YaHei UI", 12, "bold"), 
                           fg=self.text_color, bg=usd_frame["bg"])
        usd_title.pack(side=tk.LEFT, padx=(0, 20))
        
        self.usd_result_var = tk.StringVar(value="0.00")
        usd_result = tk.Label(usd_frame, textvariable=self.usd_result_var, 
                            font=("Microsoft YaHei UI", 16), 
                            fg=self.text_color, bg=usd_frame["bg"])
        usd_result.pack(side=tk.LEFT)
        
        # 分隔线
        sep = tk.Frame(result_card, height=2, bg="#e9ecef")
        sep.pack(fill=tk.X, padx=18, pady=5)
        
        # 人民币结果显示
        cny_frame = tk.Frame(result_card, bg=result_card["bg"])
        cny_frame.pack(fill=tk.X, pady=10, padx=18)
        
        cny_title = tk.Label(cny_frame, text="人民币金额", font=("Microsoft YaHei UI", 12, "bold"), 
                           fg=self.text_color, bg=cny_frame["bg"])
        cny_title.pack(side=tk.LEFT, padx=(0, 20))
        
        self.cny_result_var = tk.StringVar(value="0.00")
        cny_result = tk.Label(cny_frame, textvariable=self.cny_result_var, 
                            font=("Microsoft YaHei UI", 16), 
                            fg=self.primary_color, bg=cny_frame["bg"])
        cny_result.pack(side=tk.LEFT)
        
        # 绑定事件
        self.usd_entry.bind("<Return>", lambda e: self.convert_currency())
        self.usd_entry.bind("<KeyRelease>", lambda e: self.validate_currency_input())
        rate_entry.bind("<KeyRelease>", lambda e: self.update_manual_rate())

    def validate_currency_input(self):
        """验证货币输入是否为有效数字"""
        value = self.usd_value_var.get().strip()
        if not value:
            return

        try:
            # 允许小数输入
            float(value)
            self.status_var.set("准备就绪")
        except ValueError:
            self.status_var.set("请输入有效的数字")
            
    def update_manual_rate(self):
        """更新手动设置的汇率"""
        try:
            rate = float(self.rate_var.get())
            if rate <= 0:
                self.status_var.set("汇率必须大于0")
                return
                
            self.exchange_rate = rate
            self.status_var.set(f"已手动更新汇率: 1美元 = {rate}人民币")
            self.update_time_var.set("手动设置")
            # 如果已有输入，自动转换
            if self.usd_value_var.get().strip():
                self.convert_currency()
        except ValueError:
            self.status_var.set("请输入有效的汇率数字")
            
    def get_exchange_rate(self):
        """获取实时汇率数据（从中国银行官网获取）"""
        self.status_var.set("正在获取最新汇率...")
        
        # 检查是否安装了BeautifulSoup
        if not BS4_AVAILABLE:
            self.status_var.set("未安装BeautifulSoup库，无法从中国银行网站获取汇率。使用备用数据源...")
            self.get_exchange_rate_backup()
            return
            
        try:
            # 使用中国银行官网获取美元兑人民币汇率
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            # 中国银行外汇牌价网址
            url = "https://www.boc.cn/sourcedb/whpj/enindex_1619.html"
            
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                # 使用BeautifulSoup解析HTML
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 找到汇率表格
                table = soup.find('table', {'bgcolor': '#EAEAEA'})
                if table:
                    # 查找所有行
                    rows = table.find_all('tr')
                    
                    # 遍历行寻找USD(美元)汇率
                    for row in rows[1:]:  # 跳过表头行
                        cells = row.find_all('td')
                        if len(cells) >= 7:  # 确保行有足够的单元格
                            currency_name = cells[0].text.strip()
                            if currency_name == 'USD':
                                # 获取卖出价作为汇率
                                selling_rate_text = cells[3].text.strip()
                                if selling_rate_text and selling_rate_text != '--':
                                    try:
                                        selling_rate = float(selling_rate_text)
                                        # 中行牌价是按照100外币单位显示的，需要除以100
                                        self.exchange_rate = selling_rate / 100
                                        self.rate_var.set(str(round(self.exchange_rate, 4)))
                                        self.last_rate_update = datetime.datetime.now()
                                        
                                        # 更新状态和时间显示
                                        self.status_var.set(f"汇率更新成功: 1美元 = {self.exchange_rate}人民币")
                                        self.update_time_var.set(f"更新于: {self.last_rate_update.strftime('%Y-%m-%d %H:%M')}")
                                        
                                        # 如果已有输入，自动转换
                                        if self.usd_value_var.get().strip():
                                            self.convert_currency()
                                        return
                                    except ValueError:
                                        self.status_var.set(f"解析汇率数据失败，数值格式错误: {selling_rate_text}")
                
                # 尝试使用中间价(Middle Rate)
                table = soup.find('table', {'bgcolor': '#EAEAEA'})
                if table:
                    for row in table.find_all('tr')[1:]:
                        cells = row.find_all('td')
                        if len(cells) >= 7:
                            currency_name = cells[0].text.strip()
                            if currency_name == 'USD':
                                middle_rate_text = cells[5].text.strip()
                                if middle_rate_text and middle_rate_text != '--':
                                    try:
                                        middle_rate = float(middle_rate_text)
                                        self.exchange_rate = middle_rate / 100
                                        self.rate_var.set(str(round(self.exchange_rate, 4)))
                                        self.last_rate_update = datetime.datetime.now()
                                        
                                        self.status_var.set(f"汇率更新成功(使用中间价): 1美元 = {self.exchange_rate}人民币")
                                        self.update_time_var.set(f"更新于: {self.last_rate_update.strftime('%Y-%m-%d %H:%M')}")
                                        
                                        if self.usd_value_var.get().strip():
                                            self.convert_currency()
                                        return
                                    except ValueError:
                                        pass
                
                # 如果未找到USD汇率或解析失败，尝试备用方法
                self.status_var.set("未在中国银行网站找到美元汇率，使用备用数据源...")
                self.get_exchange_rate_backup()
            else:
                # 如果请求失败，尝试备用方法
                self.status_var.set("无法连接到中国银行网站，使用备用数据源...")
                self.get_exchange_rate_backup()
        except Exception as e:
            self.status_var.set(f"获取汇率失败: {str(e)}")
            # 尝试备用方法
            self.get_exchange_rate_backup()
            
    def get_exchange_rate_backup(self):
        """备用方法获取汇率（使用开放API）"""
        try:
            # 首先尝试使用第一个备用API
            response = requests.get("https://open.er-api.com/v6/latest/USD", timeout=10)
            data = response.json()
            
            if response.status_code == 200 and "rates" in data and "CNY" in data["rates"]:
                self.exchange_rate = data["rates"]["CNY"]
                self.rate_var.set(str(round(self.exchange_rate, 4)))
                self.last_rate_update = datetime.datetime.now()
                
                # 更新状态和时间显示
                self.status_var.set(f"汇率更新成功(备用源1): 1美元 = {self.exchange_rate}人民币")
                self.update_time_var.set(f"更新于: {self.last_rate_update.strftime('%Y-%m-%d %H:%M')}")
                
                # 如果已有输入，自动转换
                if self.usd_value_var.get().strip():
                    self.convert_currency()
                return
                
            # 如果第一个备用API失败，尝试第二个备用API
            self.status_var.set("正在尝试第二个备用数据源...")
            response = requests.get("https://api.exchangerate-api.com/v4/latest/USD", timeout=10)
            data = response.json()
            
            if response.status_code == 200 and "rates" in data and "CNY" in data["rates"]:
                self.exchange_rate = data["rates"]["CNY"]
                self.rate_var.set(str(round(self.exchange_rate, 4)))
                self.last_rate_update = datetime.datetime.now()
                
                # 更新状态和时间显示
                self.status_var.set(f"汇率更新成功(备用源2): 1美元 = {self.exchange_rate}人民币")
                self.update_time_var.set(f"更新于: {self.last_rate_update.strftime('%Y-%m-%d %H:%M')}")
                
                # 如果已有输入，自动转换
                if self.usd_value_var.get().strip():
                    self.convert_currency()
                return
            
            # 所有API都失败，提示用户手动设置
            self.status_var.set("无法获取汇率数据，请手动设置汇率或稍后再试")
        except Exception as e:
            self.status_var.set(f"获取汇率失败: {str(e)}，请手动设置汇率")
            
    def convert_currency(self):
        """将美元转换为人民币"""
        value = self.usd_value_var.get().strip()
        if not value:
            return
            
        try:
            # 获取美元金额
            usd_amount = float(value)
            
            # 转换为人民币
            cny_amount = usd_amount * self.exchange_rate
            
            # 格式化显示
            usd_formatted = f"${usd_amount:,.2f}"
            cny_formatted = f"¥{cny_amount:,.2f}"
            
            # 更新结果显示
            self.usd_result_var.set(usd_formatted)
            self.cny_result_var.set(cny_formatted)
            
            # 更新状态栏
            self.status_var.set(f"已将 {usd_formatted} 转换为 {cny_formatted}")
            
        except ValueError:
            self.status_var.set("请输入有效的数字")
        except Exception as e:
            self.status_var.set(f"转换错误: {str(e)}")

if __name__ == "__main__":
    # 创建主窗口
    root = tk.Tk()
    root.title("单位转换工具")

    # 设置应用图标（如果需要）
    try:
        root.iconbitmap("icon.ico")
    except:
        pass

    # 使窗口居中显示
    # 获取屏幕尺寸
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()

    # 获取窗口尺寸
    window_width = 700
    window_height = 600

    # 计算居中位置
    center_x = int((screen_width - window_width) / 2)
    center_y = int((screen_height - window_height) / 2)

    # 设置窗口位置
    root.geometry(f"{window_width}x{window_height}+{center_x}+{center_y}")

    # 创建应用实例
    app = ConverterApp(root)

    # 运行主循环
    root.mainloop()


