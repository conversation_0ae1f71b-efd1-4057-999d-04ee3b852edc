#! /usr/bin/env python
#  -*- coding: utf-8 -*-

import sys
import requests
import threading
import json
import pyperclip
from tkinter import messagebox, Menu
from requests.packages.urllib3.exceptions import InsecureRequestWarning
import time

requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

try:
    import Tkinter as tk
except ImportError:
    import tkinter as tk

try:
    import ttk
    py3 = False
except ImportError:
    import tkinter.ttk as ttk
    py3 = True

# 导入自定义模块
from config import UI, LANGUAGES, APP, FILES, API
from history import TranslationHistory

class StatusBar(tk.Frame):
    """状态栏组件"""
    
    def __init__(self, master, **kwargs):
        """初始化状态栏"""
        tk.Frame.__init__(self, master, **kwargs)
        self.label = tk.Label(self, bd=1, relief=tk.SUNKEN, anchor=tk.W,
                          font=UI['font'])
        self.label.pack(fill=tk.X)
        
    def set(self, text):
        """设置状态栏文本"""
        self.label.config(text=text)
        self.label.update_idletasks()
        
    def clear(self):
        """清空状态栏"""
        self.label.config(text="")
        self.label.update_idletasks()

def vp_start_gui():
    '''Starting point when module is the main routine.'''
    global root
    root = tk.Tk()
    root.configure(background=UI['bg'])
    
    # 设置窗口大小
    window_width = UI['width']
    window_height = UI['height']
    
    # 获取屏幕尺寸
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    
    # 计算窗口位置
    x = (screen_width - window_width) // 2
    y = (screen_height - window_height) // 2
    
    # 设置窗口大小和位置
    root.geometry(f"{window_width}x{window_height}+{x}+{y}")
    
    # 设置ttk样式
    style = ttk.Style()
    style.configure('TFrame', background=UI['bg'])
    style.configure('TLabel', background=UI['bg'], foreground=UI['fg'], font=UI['font'])
    
    # 配置Button样式
    style.configure('TButton',
                   font=UI['button_font'],
                   background=UI['button_bg'],
                   foreground=UI['button_fg'],
                   bordercolor=UI['border'],
                   focuscolor=UI['highlight_bg'],
                   lightcolor=UI['button_bg'],
                   darkcolor=UI['button_bg'])
    
    # 配置Combobox样式
    style.configure('TCombobox',
                   fieldbackground=UI['text_bg'],
                   background=UI['button_bg'],
                   foreground=UI['text_fg'],
                   arrowcolor=UI['fg'],
                   selectbackground=UI['highlight_bg'],
                   selectforeground=UI['highlight_fg'])
    
    # 配置Checkbutton样式
    style.configure('TCheckbutton',
                   background=UI['bg'],
                   foreground=UI['fg'],
                   font=UI['font'])
    
    # 配置滚动条样式
    style.configure('TScrollbar',
                   background=UI['button_bg'],
                   arrowcolor=UI['fg'],
                   bordercolor=UI['border'],
                   troughcolor=UI['bg'],
                   gripcount=0)
    
    top = Toplevel1(root)
    root.mainloop()

class Toplevel1:
    def __init__(self, top=None):
        '''This class configures and populates the toplevel window.'''
        _bgcolor = UI['bg']
        _fgcolor = UI['fg']
        _buttoncolor = UI['button_bg']
        _buttonfg = UI['button_fg']

        self.top = top
        self.top.title(UI['title'])
        self.top.configure(background=_bgcolor)
        self.top.geometry(f"{UI['width']}x{UI['height']}")
        self.top.minsize(800, 600)

        # 初始化翻译历史记录
        self.history_manager = TranslationHistory(FILES['history'])
        
        # 初始化自动复制设置
        self.auto_copy = tk.BooleanVar(value=True)
        
        # 初始化语言设置
        self.from_lang = tk.StringVar(value='auto')
        self.to_lang = tk.StringVar(value='zh')
        
        # 使用定时器定期保存窗口状态
        self._last_save_time = 0
        self._save_interval = 5000  # 5秒
        self._schedule_state_save()
        
        # 加载窗口状态
        self._load_window_state()

        # 创建菜单栏
        self._create_menu()

        # 主框架
        main_frame = ttk.Frame(top)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=UI['padding'], pady=UI['padding'])

        # 语言选择和交换按钮区域
        lang_frame = ttk.Frame(main_frame)
        lang_frame.pack(fill=tk.X, pady=(0, UI['padding']))

        # 源语言选择
        from_lang_label = ttk.Label(lang_frame, text="源语言:", font=UI['font'])
        from_lang_label.pack(side=tk.LEFT, padx=(0, 5))

        self.from_lang_combo = ttk.Combobox(lang_frame, 
                                        values=list(LANGUAGES.values()),
                                        width=10,
                                        font=UI['font'],
                                        state='readonly')
        self.from_lang_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.from_lang_combo.set('自动检测')

        # 交换语言按钮
        self.swap_btn = ttk.Button(lang_frame, text="⇄", width=3, command=self.swap_languages)
        self.swap_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 目标语言选择
        to_lang_label = ttk.Label(lang_frame, text="目标语言:", font=UI['font'])
        to_lang_label.pack(side=tk.LEFT, padx=(0, 5))

        self.to_lang_combo = ttk.Combobox(lang_frame, 
                                        values=list(LANGUAGES.values()),
                                        width=10,
                                        font=UI['font'],
                                        state='readonly')
        self.to_lang_combo.pack(side=tk.LEFT)
        self.to_lang_combo.set('中文')
        self.to_lang_combo.bind('<<ComboboxSelected>>', self.on_language_change)

        # 自动复制选项
        auto_copy_check = ttk.Checkbutton(lang_frame, text="自动复制结果", 
                                        variable=self.auto_copy)
        auto_copy_check.pack(side=tk.RIGHT)

        # 文本区域
        text_frame = ttk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, pady=(0, UI['padding']))

        # 源文本区域框架
        original_frame = ttk.Frame(text_frame)
        original_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, UI['padding']//2))
        
        # 源文本区域
        self.original_text = tk.Text(original_frame, 
                                  wrap=tk.WORD,
                                  width=40, 
                                  height=UI['text']['height'],
                                  font=UI['text_font'],
                                  relief=UI['text']['relief'],
                                  bd=UI['text']['borderwidth'],
                                  bg=UI['text_bg'],
                                  fg=UI['text_fg'],
                                  padx=UI['text']['padx'],
                                  pady=UI['text']['pady'])
        self.original_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 源文本滚动条
        original_scrollbar = ttk.Scrollbar(original_frame, orient=tk.VERTICAL,
                                       command=self.original_text.yview)
        self.original_text.configure(yscrollcommand=original_scrollbar.set)
        original_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 目标文本区域框架
        now_frame = ttk.Frame(text_frame)
        now_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(UI['padding']//2, 0))
        
        # 目标文本区域
        self.now_text = tk.Text(now_frame, 
                              wrap=tk.WORD,
                              width=40, 
                              height=UI['text']['height'],
                              font=UI['text_font'],
                              relief=UI['text']['relief'],
                              bd=UI['text']['borderwidth'],
                              bg=UI['text_bg'],
                              fg=UI['text_fg'],
                              padx=UI['text']['padx'],
                              pady=UI['text']['pady'])
        self.now_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 目标文本滚动条
        now_scrollbar = ttk.Scrollbar(now_frame, orient=tk.VERTICAL,
                                  command=self.now_text.yview)
        self.now_text.configure(yscrollcommand=now_scrollbar.set)
        now_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定滚动同步事件
        self.original_text.bind("<MouseWheel>", self._on_mousewheel)
        self.now_text.bind("<MouseWheel>", self._on_mousewheel)
        self.original_text.bind("<Up>", self._on_up_down)
        self.now_text.bind("<Up>", self._on_up_down)
        self.original_text.bind("<Down>", self._on_up_down)
        self.now_text.bind("<Down>", self._on_up_down)
        self.original_text.bind("<Prior>", self._on_page_updown)
        self.now_text.bind("<Prior>", self._on_page_updown)
        self.original_text.bind("<Next>", self._on_page_updown)
        self.now_text.bind("<Next>", self._on_page_updown)

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(UI['padding'], 0))

        # 翻译按钮
        self.translateBtn = ttk.Button(button_frame, text='翻译',
                                   command=self.translate,
                                   width=UI['button']['width'])
        self.translateBtn.pack(side=tk.LEFT, padx=(0, UI['button']['padding']))

        # 清空按钮
        self.clearBtn = ttk.Button(button_frame, text='清空',
                               command=self.clear_all,
                               width=UI['button']['width'])
        self.clearBtn.pack(side=tk.LEFT, padx=UI['button']['padding'])

        # 复制原文按钮
        copy_source_btn = ttk.Button(button_frame, text='复制原文',
                                 command=lambda: self.copy_text(self.original_text),
                                 width=UI['button']['width'])
        copy_source_btn.pack(side=tk.LEFT, padx=UI['button']['padding'])

        # 复制译文按钮
        copy_target_btn = ttk.Button(button_frame, text='复制译文',
                                command=lambda: self.copy_text(self.now_text),
                                width=UI['button']['width'])
        copy_target_btn.pack(side=tk.LEFT, padx=UI['button']['padding'])

        # 历史记录按钮
        self.historyBtn = ttk.Button(button_frame, text='历史记录',
                                 command=self.show_history,
                                 width=UI['button']['width'])
        self.historyBtn.pack(side=tk.RIGHT)

        # 状态栏
        self.status_bar = StatusBar(top)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        self.status_bar.set("就绪")

        # 创建右键菜单
        self._create_text_context_menu(self.original_text)
        self._create_text_context_menu(self.now_text)

        # 绑定快捷键
        self._bind_shortcuts()

    def _create_menu(self):
        """创建菜单栏"""
        menubar = Menu(self.top)
        
        # 文件菜单
        file_menu = Menu(menubar, tearoff=0)
        file_menu.add_command(label="新建", command=self.clear_all, accelerator="Ctrl+N")
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.top.quit, accelerator="Alt+F4")
        menubar.add_cascade(label="文件", menu=file_menu)
        
        # 编辑菜单
        edit_menu = Menu(menubar, tearoff=0)
        edit_menu.add_command(label="撤销", command=self._undo, accelerator="Ctrl+Z")
        edit_menu.add_command(label="重做", command=self._redo, accelerator="Ctrl+Y")
        edit_menu.add_separator()
        edit_menu.add_command(label="剪切", command=self._cut, accelerator="Ctrl+X")
        edit_menu.add_command(label="复制", command=self._copy, accelerator="Ctrl+C")
        edit_menu.add_command(label="粘贴", command=self._paste, accelerator="Ctrl+V")
        edit_menu.add_separator()
        edit_menu.add_command(label="全选", command=self._select_all, accelerator="Ctrl+A")
        menubar.add_cascade(label="编辑", menu=edit_menu)
        
        # 翻译菜单
        translate_menu = Menu(menubar, tearoff=0)
        translate_menu.add_command(label="翻译", command=self.translate, accelerator="F5")
        translate_menu.add_command(label="清空", command=self.clear_all, accelerator="F8")
        translate_menu.add_separator()
        translate_menu.add_command(label="历史记录", command=self.show_history, accelerator="F3")
        menubar.add_cascade(label="翻译", menu=translate_menu)
        
        # 帮助菜单
        help_menu = Menu(menubar, tearoff=0)
        help_menu.add_command(label="关于", command=self._show_about)
        menubar.add_cascade(label="帮助", menu=help_menu)
        
        self.top.config(menu=menubar)

    def _create_text_context_menu(self, text_widget):
        """为文本框创建右键菜单"""
        context_menu = Menu(text_widget, tearoff=0)
        context_menu.add_command(label="撤销", command=lambda: text_widget.edit_undo())
        context_menu.add_command(label="重做", command=lambda: text_widget.edit_redo())
        context_menu.add_separator()
        context_menu.add_command(label="剪切", command=lambda: text_widget.event_generate("<<Cut>>"))
        context_menu.add_command(label="复制", command=lambda: text_widget.event_generate("<<Copy>>"))
        context_menu.add_command(label="粘贴", command=lambda: text_widget.event_generate("<<Paste>>"))
        context_menu.add_separator()
        context_menu.add_command(label="全选", command=lambda: text_widget.tag_add(tk.SEL, "1.0", tk.END))
        
        text_widget.bind("<Button-3>", lambda e: self._show_context_menu(e, context_menu))

    def _show_context_menu(self, event, menu):
        """显示右键菜单"""
        menu.post(event.x_root, event.y_root)

    def _bind_shortcuts(self):
        """绑定快捷键"""
        self.top.bind("<F5>", lambda e: self.translate())
        self.top.bind("<F8>", lambda e: self.clear_all())
        self.top.bind("<F3>", lambda e: self.show_history())
        self.top.bind("<Control-n>", lambda e: self.clear_all())
        self.top.bind("<Control-a>", lambda e: self._select_all())

    def _focused_text(self):
        """获取当前焦点所在的文本框"""
        focused = self.top.focus_get()
        if focused in (self.original_text, self.now_text):
            return focused
        return self.original_text

    def _cut(self):
        """剪切文本"""
        self._focused_text().event_generate("<<Cut>>")

    def _copy(self):
        """复制文本"""
        self._focused_text().event_generate("<<Copy>>")

    def _paste(self):
        """粘贴文本"""
        self._focused_text().event_generate("<<Paste>>")

    def _undo(self):
        """撤销"""
        try:
            self._focused_text().edit_undo()
        except:
            pass

    def _redo(self):
        """重做"""
        try:
            self._focused_text().edit_redo()
        except:
            pass

    def _select_all(self, event=None):
        """全选文本"""
        self._focused_text().tag_add(tk.SEL, "1.0", tk.END)
        return "break"

    def _show_about(self):
        """显示关于对话框"""
        messagebox.showinfo("关于", 
            "翻译助手 v1.0\n\n"
            "基于DeepL的在线翻译工具\n"
            "支持多种语言互译\n\n"
            "作者: chengbeyond")

    def swap_languages(self):
        """交换源语言和目标语言"""
        from_lang = self.from_lang_combo.get()
        to_lang = self.to_lang_combo.get()
        if from_lang != '自动检测':
            self.from_lang_combo.set(to_lang)
            self.to_lang_combo.set(from_lang)
            # 如果有文本，自动触发翻译
            if self.original_text.get(1.0, tk.END).strip():
                self.translate()

    def clear_all(self):
        """清空所有文本"""
        self.original_text.delete(1.0, tk.END)
        self.now_text.delete(1.0, tk.END)
        self.set_status("已清空")

    def copy_text(self, text_widget):
        """复制指定文本框的内容"""
        text = text_widget.get(1.0, tk.END).strip()
        if text:
            pyperclip.copy(text)
            self.set_status("文本已复制到剪贴板")

    def translate(self):
        """执行翻译"""
        try:
            # 获取原文
            original_text = self.original_text.get(1.0, tk.END).strip()
            if not original_text:
                return
                
            # 设置状态
            self.status_bar.set("正在翻译...")
            self.translateBtn.configure(state='disabled')
            
            # 在后台线程中执行翻译
            threading.Thread(target=self._do_translate_async, 
                          args=(original_text,),
                          daemon=True).start()
            
        except Exception as e:
            self.status_bar.set(f"翻译出错: {str(e)}")
            self.translateBtn.configure(state='normal')
            
    def _do_translate_async(self, text):
        """在后台线程中执行翻译
        
        Args:
            text: 要翻译的文本
        """
        try:
            # 获取源语言和目标语言
            from_lang = self.from_lang_combo.get()
            if from_lang == '自动检测':
                from_lang = 'auto'
            to_lang = self.to_lang_combo.get()
            
            # 调用翻译API
            translated_text = translate_text(text, from_lang, to_lang)
            
            # 在主线程中更新UI
            self.top.after(0, self._update_translation_result, translated_text)
            
        except Exception as e:
            # 在主线程中显示错误
            self.top.after(0, self._show_error, str(e))
            
    def _update_translation_result(self, translated_text):
        """更新翻译结果
        
        Args:
            translated_text: 翻译结果文本
        """
        try:
            # 清空目标文本框
            self.now_text.delete(1.0, tk.END)
            
            # 插入翻译结果
            self.now_text.insert(tk.END, translated_text)
            
            # 如果开启了自动复制
            if self.auto_copy.get():
                pyperclip.copy(translated_text)
            
            # 保存到历史记录
            source_text = self.original_text.get(1.0, tk.END).strip()
            self.history_manager.add_record(
                source_text,
                translated_text,
                self.from_lang_combo.get(),
                self.to_lang_combo.get()
            )
            
            # 更新状态
            if self.auto_copy.get():
                self.status_bar.set("翻译完成，已复制到剪贴板")
            else:
                self.status_bar.set("翻译完成")
            
        except Exception as e:
            self.status_bar.set(f"更新翻译结果出错: {str(e)}")
        finally:
            # 恢复翻译按钮状态
            self.translateBtn.configure(state='normal')
            
    def _show_error(self, message):
        """显示错误信息"""
        self.status_bar.set(f"错误: {message}")
        self.translateBtn.configure(state='normal')

    def show_history(self):
        def apply_history(record):
            self.original_text.delete(1.0, tk.END)
            self.original_text.insert(1.0, record['source_text'])
            self.now_text.delete(1.0, tk.END)
            self.now_text.insert(1.0, record['translated_text'])
            self.from_lang_combo.set(record['from_lang'])
            self.to_lang_combo.set(record['to_lang'])

        history_dialog = HistoryDialog(self.top, self.history_manager, apply_history)

    def _schedule_state_save(self):
        """定期保存窗口状态"""
        current_time = int(time.time() * 1000)
        if current_time - self._last_save_time >= self._save_interval:
            self._save_window_state()
            self._last_save_time = current_time
        self.top.after(1000, self._schedule_state_save)
        
    def _save_window_state(self):
        """保存窗口状态到文件"""
        try:
            state = {
                'width': self.top.winfo_width(),
                'height': self.top.winfo_height(),
                'auto_copy': self.auto_copy.get(),
                'from_lang': self.from_lang_combo.get(),
                'to_lang': self.to_lang_combo.get()
            }
            
            # 在后台线程中保存文件
            threading.Thread(
                target=self._write_state_file,
                args=(state,),
                daemon=True
            ).start()
        except:
            pass
            
    def _write_state_file(self, state):
        """在后台线程中写入状态文件
        
        参数:
            state: 要保存的状态字典
        """
        try:
            with open('window_state.json', 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)
        except:
            pass

    def _load_window_state(self):
        """加载窗口状态"""
        try:
            with open('window_state.json', 'r', encoding='utf-8') as f:
                state = json.load(f)
                
            # 设置窗口大小
            if 'width' in state and 'height' in state:
                self.top.geometry(f"{state['width']}x{state['height']}")
            
            # 设置自动复制
            if 'auto_copy' in state:
                self.auto_copy.set(state['auto_copy'])
            
            # 设置语言
            if 'from_lang' in state:
                self.from_lang_combo.set(state['from_lang'])
            if 'to_lang' in state:
                self.to_lang_combo.set(state['to_lang'])
        except:
            # 设置默认窗口大小
            self.top.geometry("800x600")

    def set_status(self, text):
        """设置状态栏文本"""
        self.status_bar.set(text)

    def _on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        # 获取当前滚动位置
        source = event.widget
        target = self.now_text if source == self.original_text else self.original_text
        
        # Windows下滚轮事件的delta值需要除以120
        delta = -1 * (event.delta // 120)
        
        # 同步滚动两个文本框
        source.yview_scroll(delta, "units")
        target.yview_scroll(delta, "units")
        return "break"

    def _on_up_down(self, event):
        """处理上下键事件"""
        source = event.widget
        target = self.now_text if source == self.original_text else self.original_text
        
        if event.keysym == "Up":
            delta = -1
        else:
            delta = 1
            
        source.yview_scroll(delta, "units")
        target.yview_scroll(delta, "units")
        return "break"

    def _on_page_updown(self, event):
        """处理Page Up/Down事件"""
        source = event.widget
        target = self.now_text if source == self.original_text else self.original_text
        
        if event.keysym == "Prior":  # Page Up
            delta = -1
        else:  # Page Down
            delta = 1
            
        source.yview_scroll(delta, "pages")
        target.yview_scroll(delta, "pages")
        return "break"

    def on_language_change(self, event):
        """语言选择改变事件"""
        pass

# 历史记录对话框
class HistoryDialog(tk.Toplevel):
    def __init__(self, parent, history_manager, apply_callback):
        super().__init__(parent)
        self.title("翻译历史记录")
        self.geometry("800x500")
        self.minsize(600, 400)
        self.history_manager = history_manager
        self.apply_callback = apply_callback

        # 创建界面
        self._create_widgets()
        self._load_history()

        # 设置模态
        self.transient(parent)
        self.grab_set()
        self.protocol("WM_DELETE_WINDOW", self.destroy)

    def _create_widgets(self):
        """创建界面组件"""
        # 创建工具栏
        toolbar = ttk.Frame(self)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)
        
        # 刷新按钮
        refresh_btn = ttk.Button(toolbar, text="刷新", 
                            command=self._load_history,
                            width=15)
        refresh_btn.pack(side=tk.LEFT, padx=5)
        
        # 清空按钮
        clear_btn = ttk.Button(toolbar, text="清空历史", 
                            command=self._clear_history,
                            width=15)
        clear_btn.pack(side=tk.LEFT, padx=5)

        # 创建Treeview
        columns = ("时间", "源语言", "目标语言", "原文", "译文")
        self.tree = ttk.Treeview(self, columns=columns, show="headings")
        
        # 设置字体
        style = ttk.Style()
        style.configure("Treeview", font=UI['font'])
        style.configure("Treeview.Heading", font=UI['font'])

        # 设置列标题
        for col in columns:
            self.tree.heading(col, text=col)

        # 设置列宽
        self.tree.column("时间", width=150)
        self.tree.column("源语言", width=80)
        self.tree.column("目标语言", width=80)
        self.tree.column("原文", width=200)
        self.tree.column("译文", width=200)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(self, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscroll=scrollbar.set)

        # 布局
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定双击事件
        self.tree.bind("<Double-1>", self._on_item_double_click)

        # 绑定右键菜单
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="应用此记录", command=self._apply_selected)
        self.context_menu.add_command(label="删除此记录", command=self._delete_selected)
        self.tree.bind("<Button-3>", self._show_context_menu)

    def _load_history(self):
        # 清空现有记录
        for item in self.tree.get_children():
            self.tree.delete(item)

        # 加载历史记录
        history = self.history_manager.get_history()
        for i, record in enumerate(history):
            values = (
                record["datetime"],
                LANGUAGES.get(record["from_lang"], record["from_lang"]),
                LANGUAGES.get(record["to_lang"], record["to_lang"]),
                record["source_text"][:50] + ("..." if len(record["source_text"]) > 50 else ""),
                record["translated_text"][:50] + ("..." if len(record["translated_text"]) > 50 else "")
            )
            self.tree.insert("", tk.END, iid=str(i), values=values)

    def _clear_history(self):
        if messagebox.askyesno("确认", "确定要清空所有历史记录吗？"):
            self.history_manager.clear_history()
            self._load_history()

    def _on_item_double_click(self, event):
        self._apply_selected()

    def _apply_selected(self):
        selected = self.tree.selection()
        if not selected:
            return

        # 获取选中的记录
        index = int(selected[0])
        record = self.history_manager.get_history()[index]

        # 调用回调函数
        self.apply_callback(record)

        # 关闭对话框
        self.destroy()

    def _delete_selected(self):
        selected = self.tree.selection()
        if not selected:
            return

        # 获取选中的记录索引
        index = int(selected[0])

        # 删除记录
        if self.history_manager.delete_record(index):
            self._load_history()

    def _show_context_menu(self, event):
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

if __name__ == '__main__':
    # 设置全局异常处理
    def handle_exception(exc_type, exc_value, exc_traceback):
        error_msg = f"发生错误: {str(exc_value)}"
        messagebox.showerror("程序错误", error_msg)

    sys.excepthook = handle_exception

    def translate_text(text, from_lang, to_lang):
        """调用DeepL翻译API"""
        # 根据显示名称查找对应的语言代码
        def get_lang_code(lang_name):
            for code, name in LANGUAGES.items():
                if name == lang_name:
                    return code
            return lang_name
        
        # 转换语言代码
        source_lang = from_lang if from_lang == 'auto' else get_lang_code(from_lang)
        target_lang = get_lang_code(to_lang)
        
        # 自动检测语言
        if source_lang == 'auto':
            # 检查文本包含的字符类型
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in text)
            has_japanese = any('\u3040' <= char <= '\u30ff' for char in text)
            
            # 只有检测到中文或日语时才设置源语言
            if has_chinese:
                source_lang = 'ZH'
            elif has_japanese:
                source_lang = 'JA'
            # 如果无法识别是中文或日语，保持auto让API自动检测
        
        payload = {
            "text": text,
            "source_lang": source_lang.upper(),
            "target_lang": target_lang.upper()
        }
        
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
        
        try:
            print(f"发送请求到: {API['url']}")
            print(f"请求参数: {payload}")
            
            response = requests.post(API['url'], headers=headers, json=payload, verify=False, timeout=10)
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code != 200:
                raise Exception(f"API返回错误: {response.status_code}\n响应内容: {response.text}")
            
            data = response.json()
            if 'data' not in data:
                raise Exception(f"API返回格式错误: {data}")
            return data['data']
            
        except requests.RequestException as e:
            raise Exception(f"翻译请求失败: {str(e)}")
        except (KeyError, json.JSONDecodeError) as e:
            raise Exception(f"解析翻译结果失败: {str(e)}\n响应内容: {response.text if 'response' in locals() else '无响应'}")
    
    vp_start_gui()
