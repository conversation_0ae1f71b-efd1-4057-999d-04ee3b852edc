#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import winreg
import shutil
import logging
import ctypes
import stat
from pathlib import Path
import subprocess
from datetime import datetime
import win32file
import time

# 获取脚本所在目录
SCRIPT_DIR = Path(__file__).parent.absolute()

# 文档路径配置
CUSTOM_DOCUMENTS_PATH = Path('G:/Documents')  # 自定义文档路径
DEFAULT_DOCUMENTS_PATH = Path(os.path.expanduser('~')) / 'Documents'  # 默认文档路径

def is_admin():
    """检查是否具有管理员权限"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """以管理员权限重新运行程序"""
    try:
        if not is_admin():
            # 获取当前脚本的路径
            script = os.path.abspath(sys.argv[0])
            params = ' '.join(sys.argv[1:])
            
            # 使用Python解释器重新运行脚本
            python_exe = sys.executable
            cmd = f'"{python_exe}" "{script}" {params}'
            
            print('\n正在以管理员权限重新启动程序...')
            print('请在UAC提示窗口中选择"是"。')
            print('如果没有看到UAC提示，请右键点击程序选择"以管理员身份运行"。')
            
            # 请求UAC权限
            ctypes.windll.shell32.ShellExecuteW(None, "runas", python_exe, f'"{script}" {params}', None, 1)
            
            # 等待用户确认
            input('\n按Enter键关闭此窗口...')
            
            # 退出当前实例
            sys.exit()
            
    except Exception as e:
        print(f'请求管理员权限时出错: {e}')
        input('\n按Enter键关闭此窗口...')
        sys.exit(1)

# 配置日志
# LOG_FILE = SCRIPT_DIR / 'dmitrirender_reset.log'
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(levelname)s - %(message)s',
#     handlers=[
#         logging.StreamHandler(),
#         logging.FileHandler(LOG_FILE, encoding='utf-8')
#     ]
# )
# logger = logging.getLogger(__name__)

def delete_registry():
    """删除DmitriRender的注册表项"""
    try:
        # 尝试打开注册表项
        try:
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r'SOFTWARE\DmitriRender', 0, winreg.KEY_ALL_ACCESS)
            winreg.CloseKey(key)
        except WindowsError as e:
            if e.winerror == 2:  # 找不到注册表项
                # logger.info('注册表项不存在，无需删除')
                return True
            # logger.error(f'访问注册表项时出错: {e}')
            return False

        # 使用命令行方式删除注册表项
        try:
            cmd = 'reg delete "HKEY_CURRENT_USER\\SOFTWARE\\DmitriRender" /f'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                # logger.info('成功删除注册表项 HKEY_CURRENT_USER\\SOFTWARE\\DmitriRender')
                return True
            else:
                # logger.error(f'删除注册表项失败: {result.stderr}')
                return False
                
        except subprocess.SubprocessError as e:
            # logger.error(f'执行reg delete命令时出错: {e}')
            return False
            
    except Exception as e:
        # logger.error(f'删除注册表项时出错: {e}')
        return False

def get_desktop_ini_paths():
    """获取所有可能的desktop.ini路径"""
    paths = [
        CUSTOM_DOCUMENTS_PATH / 'desktop.ini',
        DEFAULT_DOCUMENTS_PATH / 'desktop.ini'
    ]
    return [p for p in paths if p.exists()]

def backup_desktop_ini():
    """备份所有找到的desktop.ini文件"""
    try:
        paths = get_desktop_ini_paths()
        if not paths:
            print('未找到任何desktop.ini文件，无需备份')
            return True
        
        for desktop_ini_path in paths:
            try:
                print(f'正在准备备份文件: {desktop_ini_path}')
                if not take_ownership_and_grant_permissions(desktop_ini_path):
                    print(f'无法获取文件权限: {desktop_ini_path}')
                    return False
                backup_name = f'desktop.ini.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
                backup_path = SCRIPT_DIR / backup_name
                with open(desktop_ini_path, 'rb') as src:
                    content = src.read()
                with open(backup_path, 'wb') as dst:
                    dst.write(content)
                print(f'已备份desktop.ini到 {backup_path}')
            except Exception as e:
                print(f'备份 {desktop_ini_path} 时出错: {e}')
                return False
        return True
    except Exception as e:
        # logger.error(f'备份desktop.ini时出错: {e}')
        return False

def make_file_writable(file_path):
    """使文件可写"""
    try:
        # 获取当前权限
        current_permissions = os.stat(file_path).st_mode
        # 添加写入权限
        new_permissions = current_permissions | stat.S_IWRITE
        os.chmod(file_path, new_permissions)
        return True
    except Exception as e:
        # logger.error(f'修改文件权限时出错: {e}')
        return False

def check_file_in_use(file_path):
    """检查文件是否被其他进程占用"""
    try:
        import win32file
        
        # 尝试以共享读方式打开文件
        handle = win32file.CreateFile(
            str(file_path),
            win32file.GENERIC_READ,
            0,  # 不共享
            None,
            win32file.OPEN_EXISTING,
            win32file.FILE_ATTRIBUTE_NORMAL,
            None
        )
        
        # 如果能打开，说明文件没有被占用
        win32file.CloseHandle(handle)
        return False
    except Exception:
        # 如果不能打开，说明文件可能被占用
        return True

def take_ownership_and_grant_permissions(file_path):
    """获取文件所有权并授予完全控制权限"""
    file_handle = None
    try:
        # logger.info(f'正在获取文件所有权和权限: {file_path}')
        file_path_str = str(file_path)
        
        # 首先检查文件是否被占用
        if check_file_in_use(file_path_str):
            # logger.error('文件当前被其他进程占用，请关闭所有可能使用此文件的程序')
            return False
        
        # 检查文件是否为隐藏和系统文件
        try:
            import win32api
            import win32con
            # 获取当前属性
            attrs = win32api.GetFileAttributes(file_path_str)
            # 移除隐藏和系统属性
            if attrs & win32con.FILE_ATTRIBUTE_HIDDEN:
                win32api.SetFileAttributes(file_path_str, attrs & ~win32con.FILE_ATTRIBUTE_HIDDEN)
            if attrs & win32con.FILE_ATTRIBUTE_SYSTEM:
                win32api.SetFileAttributes(file_path_str, attrs & ~win32con.FILE_ATTRIBUTE_SYSTEM)
        except Exception as e:
            # logger.warning(f'修改文件属性时出错（非致命）: {e}')
            pass
        
        # 设置当前进程权限
        try:
            import win32security
            import ntsecuritycon as con
            
            # 获取进程令牌
            ph = win32api.GetCurrentProcess()
            th = win32security.OpenProcessToken(ph, win32con.TOKEN_ADJUST_PRIVILEGES | win32con.TOKEN_QUERY)
            
            try:
                # 启用需要的权限
                priv_flags = (
                    (win32security.LookupPrivilegeValue(None, win32security.SE_TAKE_OWNERSHIP_NAME), win32con.SE_PRIVILEGE_ENABLED),
                    (win32security.LookupPrivilegeValue(None, win32security.SE_SECURITY_NAME), win32con.SE_PRIVILEGE_ENABLED),
                    (win32security.LookupPrivilegeValue(None, win32security.SE_BACKUP_NAME), win32con.SE_PRIVILEGE_ENABLED),
                    (win32security.LookupPrivilegeValue(None, win32security.SE_RESTORE_NAME), win32con.SE_PRIVILEGE_ENABLED),
                    (win32security.LookupPrivilegeValue(None, win32security.SE_CHANGE_NOTIFY_NAME), win32con.SE_PRIVILEGE_ENABLED)
                )
                
                # 调整权限
                win32security.AdjustTokenPrivileges(th, 0, priv_flags)
            finally:
                # 关闭令牌句柄
                win32api.CloseHandle(th)
                
        except Exception as e:
            # logger.warning(f'设置进程权限时出错（非致命）: {e}')
            pass
        
        # 先尝试直接打开文件
        try:
            file_handle = win32file.CreateFile(
                file_path_str,
                win32file.GENERIC_READ | win32file.GENERIC_WRITE,
                0,  # 不共享
                None,  # 默认安全属性
                win32file.OPEN_EXISTING,
                win32file.FILE_ATTRIBUTE_NORMAL,
                None
            )
            # 如果能打开文件，说明已经有权限了
            win32api.CloseHandle(file_handle)
            file_handle = None
            # logger.info('已有文件访问权限')
            return True
        except Exception:
            # 如果不能直接打开，继续尝试获取权限
            if file_handle:
                win32api.CloseHandle(file_handle)
                file_handle = None
        
        # 使用命令行工具修改权限
        commands = [
            # 获取所有权
            ['takeown', '/f', file_path_str, '/a'],
            # 禁用继承并删除所有权限
            ['icacls', file_path_str, '/inheritance:d', '/t', '/c'],
            ['icacls', file_path_str, '/reset', '/t', '/c'],
            # 授予完全控制权限
            ['icacls', file_path_str, '/grant:r', f'{os.getlogin()}:(F)', '/t', '/c'],
            ['icacls', file_path_str, '/grant:r', 'SYSTEM:(F)', '/t', '/c']
        ]
        
        for cmd in commands:
            try:
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    check=False,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
                if result.returncode != 0:
                    # logger.warning(f'执行命令警告 {" ".join(cmd)}: {result.stderr}')
                    pass
            except Exception as e:
                # logger.warning(f'执行命令出错（非致命）: {e}')
                pass
        
        # 使用Windows API修改文件权限
        try:
            # 获取当前用户的SID
            ph = win32api.GetCurrentProcess()
            th = win32security.OpenProcessToken(ph, win32con.TOKEN_QUERY)
            try:
                user_sid = win32security.GetTokenInformation(
                    th,
                    win32security.TokenUser
                )[0]
                
                # 创建新的安全描述符
                sd = win32security.SECURITY_DESCRIPTOR()
                
                # 设置所有者
                sd.SetSecurityDescriptorOwner(user_sid, False)
                
                # 创建DACL
                dacl = win32security.ACL()
                # 添加完全控制权限
                dacl.AddAccessAllowedAce(
                    win32security.ACL_REVISION,
                    con.FILE_ALL_ACCESS,
                    user_sid
                )
                
                # 设置DACL
                sd.SetSecurityDescriptorDacl(1, dacl, 0)
                
                # 应用安全描述符
                win32security.SetFileSecurity(
                    file_path_str,
                    win32security.OWNER_SECURITY_INFORMATION | win32security.DACL_SECURITY_INFORMATION,
                    sd
                )
            finally:
                # 关闭令牌句柄
                win32api.CloseHandle(th)
                
        except Exception as e:
            # logger.warning(f'设置文件安全描述符时出错（非致命）: {e}')
            pass
        
        # 最后检查是否可以写入文件
        try:
            # 等待一小段时间，让系统完成权限更改
            time.sleep(0.5)
            
            # 尝试打开文件
            with open(file_path_str, 'r+b') as f:
                current_position = f.tell()
                f.seek(0, os.SEEK_END)
                f.seek(current_position)
            # logger.info('成功获取文件写入权限')
            return True
        except Exception as e:
            # logger.error(f'无法获取文件写入权限: {e}')
            return False
            
    except Exception as e:
        # logger.error(f'修改文件权限时出错: {e}')
        return False
    finally:
        if file_handle:
            try:
                win32api.CloseHandle(file_handle)
            except:
                pass
                
def modify_desktop_ini():
    """修改所有找到的desktop.ini文件"""
    try:
        paths = get_desktop_ini_paths()
        if not paths:
            print('未找到任何desktop.ini文件，无需修改')
            return True
        
        success = True
        for desktop_ini_path in paths:
            try:
                print(f'正在处理文件: {desktop_ini_path}')
                temp_path = None
                if not take_ownership_and_grant_permissions(desktop_ini_path):
                    print(f'无法获取文件所有权和权限: {desktop_ini_path}')
                    success = False
                    continue
                content = None
                try:
                    with open(desktop_ini_path, 'rb') as f:
                        content = f.read()
                except Exception as e:
                    print(f'读取文件时出错: {e}')
                    success = False
                    continue
                if content is None:
                    continue
                encodings = ['utf-16', 'utf-8', 'cp950', 'gbk', 'big5', 'utf-16le', 'utf-16be']
                decoded_lines = None
                used_encoding = None
                decode_errors = []
                if content.startswith(b'\xff\xfe'):
                    encodings.insert(0, 'utf-16le')
                elif content.startswith(b'\xfe\xff'):
                    encodings.insert(0, 'utf-16be')
                elif content.startswith(b'\xef\xbb\xbf'):
                    encodings.insert(0, 'utf-8-sig')
                for encoding in encodings:
                    try:
                        decoded_text = content.decode(encoding)
                        decoded_lines = decoded_text.splitlines(keepends=True)
                        used_encoding = encoding
                        print(f'成功使用 {encoding} 编码解码文件 {desktop_ini_path}')
                        break
                    except UnicodeDecodeError as e:
                        decode_errors.append(f'{encoding}: {str(e)}')
                        continue
                    except Exception as e:
                        decode_errors.append(f'{encoding}: {str(e)}')
                        continue
                if decoded_lines is None:
                    print(f'无法使用任何编码解码文件 {desktop_ini_path}')
                    print('尝试的编码和错误：')
                    for error in decode_errors:
                        print(f'  {error}')
                    success = False
                    continue
                target_line_index = -1
                for i, line in enumerate(decoded_lines):
                    if 'IconIndex=-235' in line:
                        target_line_index = i
                        break
                if target_line_index == -1:
                    print(f'在 {desktop_ini_path} 中未找到需要删除的内容')
                    continue
                new_lines = decoded_lines[:target_line_index + 1]
                try:
                    if used_encoding in ['utf-16le', 'utf-16be', 'utf-8-sig']:
                        new_content = ''.join(new_lines).encode(used_encoding)
                    else:
                        new_content = ''.join(new_lines).encode(used_encoding)
                    temp_path = SCRIPT_DIR / 'desktop.ini.temp'
                    with open(temp_path, 'wb') as f:
                        f.write(new_content)
                        f.flush()
                        os.fsync(f.fileno())
                    os.replace(temp_path, desktop_ini_path)
                    temp_path = None
                except Exception as e:
                    print(f'写入文件时出错: {e}')
                    success = False
                    continue
                print(f'成功修改文件 {desktop_ini_path}（使用 {used_encoding} 编码）')
            except Exception as e:
                print(f'修改 {desktop_ini_path} 时出错: {e}')
                success = False
            finally:
                if temp_path and temp_path.exists():
                    try:
                        os.remove(temp_path)
                    except:
                        pass
        return success
    except Exception as e:
        # logger.error(f'修改desktop.ini时出错: {e}')
        return False

def run_program():
    """运行DmitriRender启用程序"""
    try:
        appdata_path = Path(os.getenv('APPDATA')) / 'DmitriRender' / 'x64'
        program_path = appdata_path / 'pcnsl.exe'
        
        if not program_path.exists():
            # logger.error(f'找不到程序文件: {program_path}')
            return False
            
        subprocess.Popen([str(program_path)], cwd=str(appdata_path))
        # logger.info('已启动DmitriRender启用程序')
        return True
    except Exception as e:
        # logger.error(f'启动程序时出错: {e}')
        return False

def reset_dmitrirender():
    """重置DmitriRender试用期的主函数"""
    print('\nDmitriRender试用期重置工具')
    print('='*50)
    print('整体原理：')
    print('1、删除注册表中HKEY_CURRENT_USER\\SOFTWARE\\DmitriRender')
    print('2、删除desktop.ini文件中IconIndex=-235下面的部分')
    print('3、打开启用程序重新试用')
    print('='*50)
    print('\n搜索路径：')
    print(f'1. 自定义路径: {CUSTOM_DOCUMENTS_PATH}')
    print(f'2. 默认路径: {DEFAULT_DOCUMENTS_PATH}')
    print('='*50)
    
    # 显示找到的desktop.ini文件
    ini_paths = get_desktop_ini_paths()
    if ini_paths:
        print('\n找到以下desktop.ini文件：')
        for i, path in enumerate(ini_paths, 1):
            print(f'{i}. {path}')
    else:
        print('\n未找到任何desktop.ini文件')
    print('='*50)
    
    input('\n按Enter键继续...')
    
    print('\n开始执行重置操作...')
    
    # 备份desktop.ini
    print('\n[1/4] 正在备份desktop.ini...')
    if not backup_desktop_ini():
        print('备份失败，操作已取消')
        input('\n按Enter键退出...')
        return
    
    # 删除注册表
    print('\n[2/4] 正在删除注册表项...')
    if not delete_registry():
        print('删除注册表失败，操作已取消')
        input('\n按Enter键退出...')
        return
    
    # 修改desktop.ini
    print('\n[3/4] 正在修改desktop.ini...')
    if not modify_desktop_ini():
        print('修改desktop.ini失败，操作已取消')
        input('\n按Enter键退出...')
        return
    
    # 运行程序
    print('\n[4/4] 正在启动DmitriRender启用程序...')
    if not run_program():
        print('启动程序失败，请手动运行启用程序')
        input('\n按Enter键退出...')
        return
    
    print('\n操作已完成！请在弹出的窗口中：')
    print('1. 选择"启用"')
    print('2. 点击"下一步"')
    print('3. 点击"测试"')
    input('\n按Enter键退出...')

if __name__ == '__main__':
    try:
        # 检查管理员权限
        if not is_admin():
            print('='*50)
            print('DmitriRender试用期重置工具')
            print('='*50)
            print('\n此程序需要管理员权限才能运行。')
            run_as_admin()
        else:
            reset_dmitrirender()
    except KeyboardInterrupt:
        print('\n操作已被用户取消')
        input('\n按Enter键退出...')
    except Exception as e:
        print(f'\n发生未知错误: {e}')
        # logger.exception('发生未知错误')
        input('\n按Enter键退出...')
    finally:
        # logging.shutdown()
        pass
