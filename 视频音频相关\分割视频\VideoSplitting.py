from tkinter import filedialog, messagebox
import threading
import time
import functools
try:
    import Tkinter as tk
except ImportError:
    import tkinter as tk

try:
    import ttk
    py3 = False
except ImportError:
    import tkinter.ttk as ttk
    py3 = True

# 尝试导入拖拽功能库
try:
    from tkinterdnd2 import DND_FILES, TkinterDnD
    DRAG_DROP_AVAILABLE = True
except ImportError:
    DRAG_DROP_AVAILABLE = False
    print("警告: 未安装tkinterdnd2库，拖拽功能不可用。")
    print("要启用拖拽功能，请运行: pip install tkinterdnd2")

import subprocess
import os
import re
import queue
import cv2
from PIL import Image, ImageTk
import numpy as np
import tempfile
import shutil

def center_window(window, width, height):
    """Center the window on the screen."""
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()
    x = (screen_width // 2) - (width // 2)
    y = (screen_height // 2) - (height // 2)
    window.geometry(f'{width}x{height}+{x}+{y}')

def vp_start_gui():
    # 如果支持拖拽功能，使用TkinterDnD.Tk()，否则使用普通的tk.Tk()
    if DRAG_DROP_AVAILABLE:
        root = TkinterDnD.Tk()
    else:
        root = tk.Tk()
    root.title("视频分割工具")
    root.configure(background='#f0f2f5')
    root.resizable(False, False)
    
    # 设置窗口大小并居中显示
    window_width = 900
    window_height = 600
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - window_width) // 2
    y = (screen_height - window_height) // 2
    root.geometry(f'{window_width}x{window_height}+{x}+{y}')
    
    # 创建主应用程序界面
    app = Toplevel1(root)
    
    # 开始主循环
    root.mainloop()

w = None
def create_Toplevel1(root, *args, **kwargs):
    global w, w_win, rt
    rt = root
    w = tk.Toplevel (root)
    top = Toplevel1 (w)
    VideoSplitting_support.init(w, top, *args, **kwargs)
    return (w, top)

def destroy_Toplevel1():
    global w
    w.destroy()
    w = None

class ToolTip(object):
    '''创建一个tooltip提示框'''
    def __init__(self, widget, text='widget info'):
        self.widget = widget
        self.text = text
        self.tipwindow = None
        self.id = None
        self.x = self.y = 0
        self.widget.bind('<Enter>', self.enter)
        self.widget.bind('<Leave>', self.leave)
        self.widget.bind('<Motion>', self.motion)

    def enter(self, event=None):
        '''鼠标进入控件时显示tooltip'''
        self.schedule()

    def leave(self, event=None):
        '''鼠标离开控件时隐藏tooltip'''
        self.unschedule()
        self.hidetip()

    def motion(self, event=None):
        '''鼠标移动时更新tooltip位置'''
        self.x = event.x
        self.y = event.y

    def schedule(self):
        '''计划显示tooltip'''
        self.unschedule()
        self.id = self.widget.after(500, self.showtip)  # 500ms后显示

    def unschedule(self):
        '''取消计划显示tooltip'''
        id = self.id
        self.id = None
        if id:
            self.widget.after_cancel(id)

    def showtip(self):
        '''显示tooltip'''
        if self.tipwindow:
            return
        # 获取控件的屏幕坐标
        x = self.widget.winfo_rootx() + self.x + 20
        y = self.widget.winfo_rooty() + self.y + 10
        # 创建tooltip窗口
        self.tipwindow = tw = tk.Toplevel(self.widget)
        tw.wm_overrideredirect(1)
        tw.wm_geometry(f"+{x}+{y}")
        
        # 创建标签
        label = tk.Label(tw, text=self.text, justify=tk.LEFT,
                        background="#ffffe0", relief=tk.SOLID, borderwidth=1,
                        font=("Microsoft YaHei", 9))
        label.pack()

    def hidetip(self):
        '''隐藏tooltip'''
        tw = self.tipwindow
        self.tipwindow = None
        if tw:
            tw.destroy()

def debounce(wait):
    """
    装饰器：防抖函数，限制函数在指定时间内只能执行一次
    """
    def decorator(func):
        def debounced(*args, **kwargs):
            def call_it():
                debounced._timer = None
                if not debounced._is_running:
                    debounced._is_running = True
                    try:
                        func(*args, **kwargs)
                    finally:
                        debounced._is_running = False

            if hasattr(debounced, '_timer') and debounced._timer is not None:
                debounced._timer.cancel()

            debounced._timer = threading.Timer(wait, call_it)
            debounced._timer.start()

        debounced._timer = None
        debounced._is_running = False
        return debounced
    return decorator

class VideoPreview:
    def __init__(self, parent, width=320, height=180):
        self.parent = parent
        self.width = width
        self.height = height
        self.cap = None
        self.video_path = None
        self.current_frame = None
        self.preview_running = False
        self.temp_dir = None
        self.update_lock = threading.Lock()
        self.last_update_time = 0
        self.update_interval = 0.1  # 100ms minimum interval between updates
        self.frame_cache = {}  # 帧缓存
        self.max_cache_size = 10  # 最大缓存帧数
        
        # 创建预览框架
        self.frame = tk.Frame(parent)
        self.frame.configure(background='black')
        
        # 创建预览标签
        self.preview_label = tk.Label(self.frame)
        self.preview_label.pack(expand=True, fill='both')
        self.preview_label.configure(background='black')
        
        # 创建默认预览图像（黑色背景）
        self.show_empty_preview()
        
        # 调试信息
        print(f"创建预览窗口: {self.width}x{self.height}")

    def get_video_fps(self, video_path):
        '''获取视频的帧率'''
        try:
            cmd = f'ffprobe -v error -select_streams v -of default=noprint_wrappers=1:nokey=1 -show_entries stream=r_frame_rate "{video_path}"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                fps_str = result.stdout.strip()
                if '/' in fps_str:
                    num, den = map(int, fps_str.split('/'))
                    return num / den
                return float(fps_str)
        except Exception as e:
            print(f"获取视频帧率失败: {e}")
        return 30.0  # 默认帧率

    def get_frame_at_time(self, time_str):
        '''获取指定时间的帧'''
        if self.cap is None or not self.cap.isOpened() or not self.video_path:
            print("视频未打开或无效")
            return None

        try:
            # 检查缓存
            if time_str in self.frame_cache:
                print(f"使用缓存帧: {time_str}")
                return self.frame_cache[time_str]

            # 将时间转换为秒
            if '.' in time_str:
                main_time, milliseconds = time_str.split('.')
                h, m, s = map(int, main_time.split(':'))
                seconds = h * 3600 + m * 60 + s + int(milliseconds) / 1000.0
            else:
                h, m, s = map(int, time_str.split(':'))
                seconds = h * 3600 + m * 60 + s

            # 使用ffmpeg精确提取帧
            temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            temp_file.close()
            
            try:
                # 使用ffmpeg提取精确时间点的帧
                cmd = f'ffmpeg -y -ss {seconds} -i "{self.video_path}" -vframes 1 -f image2 "{temp_file.name}"'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                
                if result.returncode == 0:
                    # 读取提取的帧
                    frame = cv2.imread(temp_file.name)
                    if frame is not None:
                        # 调整帧大小
                        frame = cv2.resize(frame, (self.width, self.height))
                        # 转换颜色空间
                        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                        
                        # 更新缓存
                        self.update_frame_cache(time_str, frame.copy())
                        
                        print(f"成功获取时间 {time_str} 的帧")
                        return frame
            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_file.name)
                except:
                    pass

            print(f"无法获取时间 {time_str} 的帧")
            return None

        except Exception as e:
            print(f"获取视频帧时出错: {e}")
            return None

    def update_frame_cache(self, time_str, frame):
        '''更新帧缓存'''
        try:
            # 如果缓存已满，删除最早的条目
            if len(self.frame_cache) >= self.max_cache_size:
                oldest_time = min(self.frame_cache.keys())
                del self.frame_cache[oldest_time]
            
            # 添加新帧到缓存
            self.frame_cache[time_str] = frame
        except Exception as e:
            print(f"更新帧缓存时出错: {e}")

    def clear_frame_cache(self):
        '''清除帧缓存'''
        self.frame_cache.clear()

    def show_empty_preview(self):
        '''显示空的预览窗口'''
        try:
            empty_image = Image.new('RGB', (self.width, self.height), 'black')
            photo = ImageTk.PhotoImage(empty_image)
            self.preview_label.configure(image=photo)
            self.preview_label.image = photo
        except Exception as e:
            print(f"显示空预览时出错: {e}")
    
    def open_video(self, video_path):
        '''打开视频文件'''
        print(f"尝试打开视频: {video_path}")
        try:
            with self.update_lock:
                # 如果已经打开了相同的视频文件，不需要重新打开
                if self.video_path == video_path and self.cap is not None and self.cap.isOpened():
                    print("视频已经打开")
                    return

                # 关闭之前的视频
                if self.cap is not None:
                    self.cap.release()
                    self.cap = None

                # 清除帧缓存
                self.clear_frame_cache()

                # 打开新视频
                self.cap = cv2.VideoCapture(video_path)
                if not self.cap.isOpened():
                    print(f"无法打开视频文件: {video_path}")
                    self.cap = None
                    self.video_path = None
                    return
                
                self.video_path = video_path
                print(f"成功打开视频: {video_path}")
        except Exception as e:
            print(f"打开视频文件时出错: {e}")
            self.cap = None
            self.video_path = None
    
    def show_frame(self, frame):
        '''显示帧'''
        try:
            if frame is not None:
                # 转换为PIL图像
                image = Image.fromarray(frame)
                # 转换为PhotoImage
                photo = ImageTk.PhotoImage(image)
                # 更新标签
                self.preview_label.configure(image=photo)
                self.preview_label.image = photo
                print("成功显示帧")
            else:
                print("显示空预览")
                self.show_empty_preview()
        except Exception as e:
            print(f"显示帧时出错: {e}")
            self.show_empty_preview()
    
    @debounce(0.1)  # 100ms防抖
    def update_preview(self, time_str):
        '''更新预览画面'''
        print(f"更新预览: {time_str}")
        current_time = time.time()
        if current_time - self.last_update_time < self.update_interval:
            print("更新间隔太短，跳过")
            return

        try:
            if self.cap is not None and self.cap.isOpened():
                frame = self.get_frame_at_time(time_str)
                if frame is not None:
                    self.preview_label.after(0, self.show_frame, frame)
                    self.last_update_time = current_time
                    print("预览更新完成")
                else:
                    print("获取帧失败")
            else:
                print("视频未打开或无效")
        except Exception as e:
            print(f"更新预览时出错: {e}")
    
    def close(self):
        '''关闭预览'''
        print("关闭预览")
        with self.update_lock:
            if self.cap is not None:
                self.cap.release()
                self.cap = None
            self.video_path = None
            self.clear_frame_cache()
            self.show_empty_preview()

class Toplevel1:
    # 处理模式配置
    MODE_CONFIG = {
        0: {
            'name': 'GPU加速',
            'icon': '🚀',
            'description': '使用NVIDIA GPU硬件加速进行视频处理，提供最快的处理速度和流畅的画面质量。需要NVIDIA显卡支持。',
            'info': '使用NVIDIA GPU加速处理，速度快且画面流畅'
        },
        1: {
            'name': 'CPU快速',
            'icon': '⚡',
            'description': '使用CPU快速处理模式，优先考虑处理速度，适合简单的视频剪切任务。',
            'info': 'CPU快速模式：速度优先，适合简单剪切'
        },
        2: {
            'name': 'CPU平衡',
            'icon': '⚖️',
            'description': 'CPU处理的平衡模式，在速度和质量之间取得较好的平衡，适合大多数常规视频处理任务。',
            'info': 'CPU平衡模式：速度与质量平衡，适合一般用途'
        },
        3: {
            'name': 'CPU高质量',
            'icon': '💎',
            'description': 'CPU高质量处理模式，优先保证视频质量，使用最佳的编码参数，适合需要高质量输出的场景。',
            'info': 'CPU高质量模式：最佳画质，适合重要视频'
        },
        4: {
            'name': '快速复制',
            'icon': '📋',
            'description': '直接复制视频流而不重新编码，速度最快，但可能在某些视频上分割点不够精确。',
            'info': '快速复制模式：无重编码，最快但可能不够精确'
        }
    }

    def __init__(self, top=None):
        '''初始化界面'''
        # 配置主窗口
        top.configure(background='#f0f2f5')

        # 创建主容器 - 使用卡片式设计
        self.main_frame = tk.Frame(top)
        self.main_frame.place(x=30, y=30, width=840, height=540)  # 调整高度适应600px窗口
        self.main_frame.configure(background='white', relief='flat', bd=0)

        # 添加阴影效果（通过背景框架模拟）
        shadow_frame = tk.Frame(top)
        shadow_frame.place(x=33, y=33, width=840, height=540)  # 调整阴影框架高度
        shadow_frame.configure(background='#e0e0e0')
        shadow_frame.lower()

        # 文件选择区域
        file_section = tk.Frame(self.main_frame)
        file_section.place(x=30, y=20, width=780, height=60)
        file_section.configure(background='#f8f9fa')

        self.Label1 = tk.Label(file_section)
        self.Label1.place(x=0, y=0, width=200, height=25)  # 左对齐
        self.Label1.configure(background='#f8f9fa', foreground='#495057')
        self.Label1.configure(font=('Microsoft YaHei', 10))
        self.Label1.configure(anchor='w')  # 添加左对齐设置
        if DRAG_DROP_AVAILABLE:
            self.Label1.configure(text='选择文件 (支持拖拽):')
        else:
            self.Label1.configure(text='选择文件:')

        # 恢复使用Text控件
        self.Text1 = tk.Text(file_section)
        self.Text1.place(x=0, y=30, width=600, height=25)  # 左对齐
        self.Text1.configure(background='white', foreground='#495057')
        self.Text1.configure(font=('Microsoft YaHei', 9))
        # 使用与LabelFrame相同的边框颜色 - 简化设置
        self.Text1.configure(relief='solid', borderwidth=1)
        # 将边框颜色设置为浅灰色
        if hasattr(self.Text1, 'config') and callable(getattr(self.Text1, 'config')):
            try:
                self.Text1.config(highlightbackground='#e0e0e0', highlightcolor='#e0e0e0')
            except:
                pass
        
        # 绑定点击事件
        self.Text1.bind("<FocusIn>", self.on_text_focus_in)
        self.Text1.bind("<Button-1>", self.on_text_click)

        self.Button1 = tk.Button(file_section)
        self.Button1.place(x=620, y=28, width=120, height=30)  # 调整位置
        self.Button1.configure(background='#007bff', foreground='white')
        self.Button1.configure(font=('Microsoft YaHei', 9, 'bold'))
        self.Button1.configure(text='浏览文件')
        self.Button1.configure(relief='flat', cursor='hand2')
        self.Button1.configure(command=self.load_file)  # 添加事件绑定

        # 时间设置区域
        time_section = tk.Frame(self.main_frame)
        time_section.place(x=30, y=100, width=780, height=90)
        time_section.configure(background='#f8f9fa')

        # 时间设置标题和提示
        time_title = tk.Label(time_section)
        time_title.place(x=0, y=0, width=80, height=25)  # 减小宽度
        time_title.configure(text="时间设置", font=('Microsoft YaHei', 12, 'bold'))
        time_title.configure(background='#f8f9fa', foreground='#2c3e50')

        time_hint = tk.Label(time_section)
        time_hint.place(x=85, y=3, width=200, height=20)  # 调整位置紧跟标题
        time_hint.configure(text="(使用鼠标滚轮调整数值)", font=('Microsoft YaHei', 8))
        time_hint.configure(background='#f8f9fa', foreground='#6c757d')

        # 开始时间标签
        start_label = tk.Label(time_section)
        start_label.place(x=0, y=35, width=80, height=25)  # 保持左对齐
        start_label.configure(text='开始时间:', background='#f8f9fa')
        start_label.configure(font=('Microsoft YaHei', 10))

        # 开始时间控件组
        start_frame = tk.Frame(time_section)
        start_frame.place(x=80, y=35, width=280, height=30)
        start_frame.configure(background='#f8f9fa')

        self.start_hour = tk.Spinbox(start_frame)
        self.start_hour.place(x=0, y=0, width=50, height=30)
        self.start_hour.configure(from_=0, to=23, format='%02.0f')
        self.start_hour.configure(font=('Consolas', 11))
        self.start_hour.delete(0, tk.END)
        self.start_hour.insert(0, '00')

        tk.Label(start_frame, text=':', font=('Consolas', 14, 'bold'),
                background='#f8f9fa').place(x=55, y=0)

        self.start_minute = tk.Spinbox(start_frame)
        self.start_minute.place(x=70, y=0, width=50, height=30)
        self.start_minute.configure(from_=0, to=59, format='%02.0f')
        self.start_minute.configure(font=('Consolas', 11))
        self.start_minute.delete(0, tk.END)
        self.start_minute.insert(0, '00')

        tk.Label(start_frame, text=':', font=('Consolas', 14, 'bold'),
                background='#f8f9fa').place(x=125, y=0)

        self.start_second = tk.Spinbox(start_frame)
        self.start_second.place(x=140, y=0, width=50, height=30)
        self.start_second.configure(from_=0, to=59, format='%02.0f')
        self.start_second.configure(font=('Consolas', 11))
        self.start_second.delete(0, tk.END)
        self.start_second.insert(0, '00')

        tk.Label(start_frame, text='.', font=('Consolas', 14, 'bold'),
                background='#f8f9fa').place(x=195, y=0)

        self.start_millisecond = tk.Spinbox(start_frame)
        self.start_millisecond.place(x=210, y=0, width=50, height=30)
        self.start_millisecond.configure(from_=0, to=999, format='%03.0f')
        self.start_millisecond.configure(font=('Consolas', 11))
        self.start_millisecond.delete(0, tk.END)
        self.start_millisecond.insert(0, '000')

        # 结束时间标签
        end_label = tk.Label(time_section)
        end_label.place(x=380, y=35, width=80, height=25)
        end_label.configure(text='结束时间:', background='#f8f9fa')
        end_label.configure(font=('Microsoft YaHei', 10))

        # 结束时间控件组
        end_frame = tk.Frame(time_section)
        end_frame.place(x=460, y=35, width=280, height=30)
        end_frame.configure(background='#f8f9fa')

        self.end_hour = tk.Spinbox(end_frame)
        self.end_hour.place(x=0, y=0, width=50, height=30)
        self.end_hour.configure(from_=0, to=23, format='%02.0f')
        self.end_hour.configure(font=('Consolas', 11))
        self.end_hour.delete(0, tk.END)
        self.end_hour.insert(0, '00')

        tk.Label(end_frame, text=':', font=('Consolas', 14, 'bold'),
                background='#f8f9fa').place(x=55, y=0)

        self.end_minute = tk.Spinbox(end_frame)
        self.end_minute.place(x=70, y=0, width=50, height=30)
        self.end_minute.configure(from_=0, to=59, format='%02.0f')
        self.end_minute.configure(font=('Consolas', 11))
        self.end_minute.delete(0, tk.END)
        self.end_minute.insert(0, '00')

        tk.Label(end_frame, text=':', font=('Consolas', 14, 'bold'),
                background='#f8f9fa').place(x=125, y=0)

        self.end_second = tk.Spinbox(end_frame)
        self.end_second.place(x=140, y=0, width=50, height=30)
        self.end_second.configure(from_=0, to=59, format='%02.0f')
        self.end_second.configure(font=('Consolas', 11))
        self.end_second.delete(0, tk.END)
        self.end_second.insert(0, '00')

        tk.Label(end_frame, text='.', font=('Consolas', 14, 'bold'),
                background='#f8f9fa').place(x=195, y=0)

        self.end_millisecond = tk.Spinbox(end_frame)
        self.end_millisecond.place(x=210, y=0, width=50, height=30)
        self.end_millisecond.configure(from_=0, to=999, format='%03.0f')
        self.end_millisecond.configure(font=('Consolas', 11))
        self.end_millisecond.delete(0, tk.END)
        self.end_millisecond.insert(0, '000')

        # 预览区域
        preview_section = tk.Frame(self.main_frame)
        preview_section.place(x=30, y=210, width=780, height=180)
        preview_section.configure(background='#f8f9fa')

        # 开始时间预览
        start_preview_frame = tk.LabelFrame(preview_section, text="开始时间预览")
        start_preview_frame.place(x=0, y=0, width=380, height=180)  # 左对齐
        start_preview_frame.configure(background='#f8f9fa', fg='#9e9e9e')  # 设置更柔和的边框颜色
        
        self.start_preview = VideoPreview(start_preview_frame)
        self.start_preview.frame.place(x=10, y=5, width=360, height=140)
        
        # 结束时间预览
        end_preview_frame = tk.LabelFrame(preview_section, text="结束时间预览")
        end_preview_frame.place(x=400, y=0, width=380, height=180)
        end_preview_frame.configure(background='#f8f9fa', fg='#9e9e9e')  # 设置更柔和的边框颜色
        
        self.end_preview = VideoPreview(end_preview_frame)
        self.end_preview.frame.place(x=10, y=5, width=360, height=140)

        # 处理模式区域
        mode_section = tk.Frame(self.main_frame)
        mode_section.place(x=30, y=400, width=780, height=60)  # 调整位置，向上移动
        mode_section.configure(background='#f8f9fa')

        # 处理模式选择框架
        mode_frame = tk.LabelFrame(mode_section, text="处理模式")
        mode_frame.place(x=0, y=0, width=500, height=60)  # 左对齐
        mode_frame.configure(background='white', fg='#9e9e9e')  # 设置更柔和的边框颜色

        # 处理方式选择：添加更多CPU处理模式
        self.process_mode = tk.IntVar()
        self.process_mode.set(0)  # 默认选择GPU模式

        # GPU模式
        self.gpu_radio = tk.Radiobutton(mode_frame, text=f"{self.MODE_CONFIG[0]['name']}", 
                                       variable=self.process_mode, value=0)
        self.gpu_radio.place(x=10, y=5)
        self.gpu_radio.configure(background='white')
        ToolTip(self.gpu_radio, self.MODE_CONFIG[0]['description'])

        # CPU快速模式
        self.cpu_fast_radio = tk.Radiobutton(mode_frame, text=f"{self.MODE_CONFIG[1]['name']}", 
                                            variable=self.process_mode, value=1)
        self.cpu_fast_radio.place(x=100, y=5)
        self.cpu_fast_radio.configure(background='white')
        ToolTip(self.cpu_fast_radio, self.MODE_CONFIG[1]['description'])

        # CPU平衡模式
        self.cpu_balanced_radio = tk.Radiobutton(mode_frame, text=f"{self.MODE_CONFIG[2]['name']}", 
                                                variable=self.process_mode, value=2)
        self.cpu_balanced_radio.place(x=190, y=5)
        self.cpu_balanced_radio.configure(background='white')
        ToolTip(self.cpu_balanced_radio, self.MODE_CONFIG[2]['description'])

        # CPU高质量模式
        self.cpu_quality_radio = tk.Radiobutton(mode_frame, text=f"{self.MODE_CONFIG[3]['name']}", 
                                               variable=self.process_mode, value=3)
        self.cpu_quality_radio.place(x=280, y=5)
        self.cpu_quality_radio.configure(background='white')
        ToolTip(self.cpu_quality_radio, self.MODE_CONFIG[3]['description'])

        # 快速复制模式
        self.copy_radio = tk.Radiobutton(mode_frame, text=f"{self.MODE_CONFIG[4]['name']}", 
                                        variable=self.process_mode, value=4)
        self.copy_radio.place(x=370, y=5)
        self.copy_radio.configure(background='white')
        ToolTip(self.copy_radio, self.MODE_CONFIG[4]['description'])

        # 说明文字
        self.info_label = tk.Label(mode_section)
        self.info_label.place(x=0, y=70, width=500, height=25)  # 左对齐
        self.info_label.configure(background='#f8f9fa', foreground='#6c757d')
        self.info_label.configure(font=('Microsoft YaHei', 9))
        self.process_mode.trace('w', self.update_mode_info)

        # 开始分割按钮
        self.Button2 = tk.Button(mode_section)
        self.Button2.place(x=620, y=15, width=120, height=40)  # 调整y坐标，使按钮稍微下移
        self.Button2.configure(background='#28a745', foreground='white')
        self.Button2.configure(font=('Microsoft YaHei', 11, 'bold'))
        self.Button2.configure(text='开始分割')
        self.Button2.configure(relief='flat', cursor='hand2')
        self.Button2.configure(command=self.split_media)

        # 进度显示区域 - 修改为始终显示并用LabelFrame包起来
        progress_section = tk.Frame(self.main_frame)
        progress_section.place(x=30, y=470, width=780, height=65)  # 增加高度
        progress_section.configure(background='#f8f9fa')
        
        # 进度框架
        progress_frame = tk.LabelFrame(progress_section, text="处理进度")
        progress_frame.place(x=0, y=0, width=780, height=65)  # 增加高度
        progress_frame.configure(background='white', fg='#9e9e9e')  # 设置更柔和的边框颜色
        
        # 进度标签
        self.progress_label = tk.Label(progress_frame)
        self.progress_label.place(x=10, y=5, width=760, height=20)  # 调整位置
        self.progress_label.configure(background='white', foreground='#495057')
        self.progress_label.configure(font=('Microsoft YaHei', 9))
        self.progress_label.configure(text="准备就绪")
        
        # 进度条
        self.progressbar = ttk.Progressbar(progress_frame)
        self.progressbar.place(x=10, y=30, width=760, height=8)  # 调整位置
        self.progressbar.configure(mode='determinate')
        
        # 进度队列用于线程间通信
        self.progress_queue = queue.Queue()
        self.total_frames = 0
        self.last_error = ""

        # 进程管理
        self.current_process = None
        self.is_processing = False

        # 绑定窗口关闭事件
        top.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 初始化拖拽功能
        if DRAG_DROP_AVAILABLE:
            self.setup_drag_drop()

        # 绑定时间变化事件
        self.bind_time_change_events()

    def setup_drag_drop(self):
        '''初始化拖拽功能'''
        try:
            # 为Text1文本框绑定拖拽事件
            self.Text1.drop_target_register(DND_FILES)
            self.Text1.dnd_bind('<<Drop>>', self.on_drop)

            # 为整个文件选择区域也绑定拖拽事件
            file_section = self.Text1.master
            file_section.drop_target_register(DND_FILES)
            file_section.dnd_bind('<<Drop>>', self.on_drop)

        except Exception as e:
            print(f"设置拖拽功能时出错: {e}")

    def on_drop(self, event):
        '''处理文件拖拽事件'''
        try:
            # 获取拖拽的文件路径
            files = event.data
            
            # 处理Windows下的文件路径
            if files.startswith('{'):
                files = files[1:]
            if files.endswith('}'):
                files = files[:-1]
                
            # 分割多个文件（如果有的话）
            file_list = files.split('} {')
            
            if file_list:
                # 处理第一个文件
                file_path = file_list[0]
                # 替换可能的转义字符
                file_path = file_path.replace('\\\\', '\\')
                # 规范化路径
                file_path = os.path.normpath(file_path)
                
                print(f"处理拖拽文件: {file_path}")  # 调试信息
                
                self.handle_dropped_file(file_path)

                # 如果拖拽了多个文件，提示用户
                if len(file_list) > 1:
                    messagebox.showinfo("提示", f"检测到{len(file_list)}个文件，已选择第一个文件。")

        except Exception as e:
            print(f"处理拖拽事件时出错: {e}")
            messagebox.showerror("错误", f"处理拖拽文件时出错: {str(e)}")

    def handle_dropped_file(self, file_path):
        '''处理拖拽的文件'''
        try:
            print(f"检查文件路径: {file_path}")
            
            # 确保文件路径是绝对路径
            file_path = os.path.abspath(file_path)
            print(f"绝对路径: {file_path}")
            
            # 检查文件是否存在
            if not os.path.isfile(file_path):
                print(f"文件不存在: {file_path}")
                messagebox.showerror("错误", f"文件不存在！\n{file_path}")
                return

            # 检查是否为视频文件
            if not self.is_video_file(file_path):
                print(f"非视频文件: {file_path}")
                messagebox.showerror("错误", "请选择视频文件！\n支持的格式：mp4, avi, mkv, mov, wmv, flv, webm, m4v, 3gp")
                return

            # 清除提示文字并设置正常颜色
            self.clear_placeholder_text()

            # 立即更新Text1内容
            self.Text1.delete(1.0, tk.END)
            self.Text1.insert(tk.END, file_path)

            # 显示加载提示
            self.show_loading_status("正在获取视频信息...")

            # 打开视频预览
            self.start_preview.open_video(file_path)
            self.end_preview.open_video(file_path)

            # 在后台线程中获取视频时长，避免界面卡顿
            thread = threading.Thread(target=self.get_video_info_async, args=(file_path,))
            thread.daemon = True
            thread.start()

        except Exception as e:
            print(f"处理拖拽文件时出错: {e}")
            messagebox.showerror("错误", f"处理文件时出错: {str(e)}")

    def is_video_file(self, file_path):
        '''检查文件是否为视频格式'''
        video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv',
                          '.webm', '.m4v', '.3gp', '.mpg', '.mpeg', '.ts', '.mts'}
        _, ext = os.path.splitext(file_path.lower())
        return ext in video_extensions

    def on_text_focus_in(self, event):
        '''Text1获得焦点时清除提示文字'''
        self.clear_placeholder_text()

    def on_text_click(self, event):
        '''Text1被点击时清除提示文字'''
        self.clear_placeholder_text()

    def clear_placeholder_text(self):
        '''清除提示文字'''
        current_text = self.Text1.get('1.0', tk.END).strip()
        if current_text == '拖拽视频文件到此处或点击浏览文件按钮...':
            self.Text1.delete('1.0', tk.END)
            self.Text1.configure(foreground='#495057')  # 恢复正常文字颜色

    def show_loading_status(self, message):
        '''显示加载状态'''
        try:
            if hasattr(self, 'progress_label'):
                self.progress_label.configure(text=message)
            print(f"状态: {message}")
        except Exception as e:
            print(f"显示状态时出错: {e}")

    def get_video_info_async(self, file_path):
        '''在后台线程中获取视频信息'''
        try:
            # 获取视频时长
            duration = self.get_video_duration(file_path)

            # 在主线程中更新UI
            if hasattr(self, 'Text1'):
                self.Text1.after(0, self.update_video_info_ui, duration)

        except Exception as e:
            print(f"获取视频信息时出错: {e}")
            # 在主线程中显示错误
            if hasattr(self, 'Text1'):
                self.Text1.after(0, self.show_video_info_error, str(e))

    def update_video_info_ui(self, duration):
        '''在主线程中更新视频信息UI'''
        try:
            if duration:
                # 设置开始时间为00:00:00
                self.set_start_time('00:00:00')
                # 设置结束时间为视频总时长
                self.set_end_time(duration)
                self.show_loading_status("视频信息获取完成")
            else:
                self.show_video_info_error("无法获取视频时长")
        except Exception as e:
            print(f"更新UI时出错: {e}")
            self.show_video_info_error(str(e))

    def show_video_info_error(self, error_msg):
        '''显示视频信息获取错误'''
        try:
            self.show_loading_status("获取视频信息失败")
            messagebox.showwarning("警告", f"无法获取视频时长: {error_msg}\n请手动填写时间")
        except Exception as e:
            print(f"显示错误信息时出错: {e}")

    def bind_time_change_events(self):
        '''绑定时间变化事件'''
        from functools import partial
        
        # 开始时间
        for widget in [self.start_hour, self.start_minute, self.start_second, self.start_millisecond]:
            widget.configure(command=partial(self.schedule_time_update, 'start'))
            widget.bind('<KeyRelease>', partial(self.on_key_release, 'start'))
            widget.bind('<FocusOut>', self.validate_start_time)
            self.bind_scroll_wheel(widget, 'start')
        
        # 结束时间
        for widget in [self.end_hour, self.end_minute, self.end_second, self.end_millisecond]:
            widget.configure(command=partial(self.schedule_time_update, 'end'))
            widget.bind('<KeyRelease>', partial(self.on_key_release, 'end'))
            widget.bind('<FocusOut>', self.validate_end_time)
            self.bind_scroll_wheel(widget, 'end')

    def bind_scroll_wheel(self, spinbox, time_type):
        '''为Spinbox绑定鼠标滚轮事件'''
        def on_scroll(event):
            try:
                if event.delta > 0:  # 向上滚动
                    spinbox.invoke('buttonup')
                else:  # 向下滚动
                    spinbox.invoke('buttondown')
                self.schedule_time_update(time_type)
            except Exception as e:
                print(f"滚轮事件处理出错: {e}")

        spinbox.bind("<MouseWheel>", on_scroll)

    def on_key_release(self, time_type, event):
        '''处理键盘释放事件'''
        self.schedule_time_update(time_type)

    @debounce(0.1)  # 100ms防抖
    def schedule_time_update(self, time_type):
        '''调度时间更新'''
        try:
            if time_type == 'start':
                self.validate_start_time()
                if hasattr(self, 'start_preview'):
                    self.start_preview.update_preview(self.get_start_time())
            else:
                self.validate_end_time()
                if hasattr(self, 'end_preview'):
                    self.end_preview.update_preview(self.get_end_time())
        except Exception as e:
            print(f"更新时间出错: {e}")

    def validate_start_time(self, event=None):
        '''验证开始时间的有效性'''
        try:
            hour = int(self.start_hour.get())
            minute = int(self.start_minute.get())
            second = int(self.start_second.get())
            millisecond = int(self.start_millisecond.get())
            
            # 确保值在有效范围内
            hour = max(0, min(hour, 23))
            minute = max(0, min(minute, 59))
            second = max(0, min(second, 59))
            millisecond = max(0, min(millisecond, 999))
            
            # 更新显示
            self.start_hour.delete(0, tk.END)
            self.start_hour.insert(0, f"{hour:02d}")
            self.start_minute.delete(0, tk.END)
            self.start_minute.insert(0, f"{minute:02d}")
            self.start_second.delete(0, tk.END)
            self.start_second.insert(0, f"{second:02d}")
            self.start_millisecond.delete(0, tk.END)
            self.start_millisecond.insert(0, f"{millisecond:03d}")
            return True
        except ValueError:
            # 如果输入无效，重置为默认值
            self.start_hour.delete(0, tk.END)
            self.start_hour.insert(0, "00")
            self.start_minute.delete(0, tk.END)
            self.start_minute.insert(0, "00")
            self.start_second.delete(0, tk.END)
            self.start_second.insert(0, "00")
            self.start_millisecond.delete(0, tk.END)
            self.start_millisecond.insert(0, "000")
            return False

    def validate_end_time(self, event=None):
        '''验证结束时间的有效性'''
        try:
            hour = int(self.end_hour.get())
            minute = int(self.end_minute.get())
            second = int(self.end_second.get())
            millisecond = int(self.end_millisecond.get())
            
            # 确保值在有效范围内
            hour = max(0, min(hour, 23))
            minute = max(0, min(minute, 59))
            second = max(0, min(second, 59))
            millisecond = max(0, min(millisecond, 999))
            
            # 更新显示
            self.end_hour.delete(0, tk.END)
            self.end_hour.insert(0, f"{hour:02d}")
            self.end_minute.delete(0, tk.END)
            self.end_minute.insert(0, f"{minute:02d}")
            self.end_second.delete(0, tk.END)
            self.end_second.insert(0, f"{second:02d}")
            self.end_millisecond.delete(0, tk.END)
            self.end_millisecond.insert(0, f"{millisecond:03d}")
            return True
        except ValueError:
            # 如果输入无效，重置为默认值
            self.end_hour.delete(0, tk.END)
            self.end_hour.insert(0, "00")
            self.end_minute.delete(0, tk.END)
            self.end_minute.insert(0, "00")
            self.end_second.delete(0, tk.END)
            self.end_second.insert(0, "00")
            self.end_millisecond.delete(0, tk.END)
            self.end_millisecond.insert(0, "000")
            return False

    def on_start_time_change(self, event=None):
        '''开始时间变化时更新预览'''
        if hasattr(self, 'start_preview'):
            self.start_preview.update_preview(self.get_start_time())

    def on_end_time_change(self, event=None):
        '''结束时间变化时更新预览'''
        if hasattr(self, 'end_preview'):
            self.end_preview.update_preview(self.get_end_time())

    def get_start_time(self):
        '''获取开始时间字符串'''
        hour = self.start_hour.get().zfill(2)
        minute = self.start_minute.get().zfill(2)
        second = self.start_second.get().zfill(2)
        millisecond = self.start_millisecond.get().zfill(3)
        return f"{hour}:{minute}:{second}.{millisecond}"

    def get_end_time(self):
        '''获取结束时间字符串'''
        hour = self.end_hour.get().zfill(2)
        minute = self.end_minute.get().zfill(2)
        second = self.end_second.get().zfill(2)
        millisecond = self.end_millisecond.get().zfill(3)
        return f"{hour}:{minute}:{second}.{millisecond}"

    def set_start_time(self, time_str):
        '''设置开始时间'''
        try:
            # 处理包含毫秒的时间格式
            if '.' in time_str:
                main_time, milliseconds = time_str.split('.')
                parts = main_time.split(':')
                self.start_millisecond.delete(0, tk.END)
                self.start_millisecond.insert(0, f"{int(milliseconds):03d}")
            else:
                parts = time_str.split(':')
                self.start_millisecond.delete(0, tk.END)
                self.start_millisecond.insert(0, "000")

            self.start_hour.delete(0, tk.END)
            self.start_hour.insert(0, f"{int(parts[0]):02d}")
            self.start_minute.delete(0, tk.END)
            self.start_minute.insert(0, f"{int(parts[1]):02d}")
            self.start_second.delete(0, tk.END)
            self.start_second.insert(0, f"{int(parts[2]):02d}")
        except:
            pass

    def set_end_time(self, time_str):
        '''设置结束时间'''
        try:
            # 处理包含毫秒的时间格式
            if '.' in time_str:
                main_time, milliseconds = time_str.split('.')
                parts = main_time.split(':')
                self.end_millisecond.delete(0, tk.END)
                self.end_millisecond.insert(0, f"{int(milliseconds):03d}")
            else:
                parts = time_str.split(':')
                self.end_millisecond.delete(0, tk.END)
                self.end_millisecond.insert(0, "000")

            self.end_hour.delete(0, tk.END)
            self.end_hour.insert(0, f"{int(parts[0]):02d}")
            self.end_minute.delete(0, tk.END)
            self.end_minute.insert(0, f"{int(parts[1]):02d}")
            self.end_second.delete(0, tk.END)
            self.end_second.insert(0, f"{int(parts[2]):02d}")
        except:
            pass

    def get_video_duration(self, file_path):
        '''获取视频时长（优化版本，添加超时机制）'''
        try:
            # 优化的ffprobe命令，添加更多参数来加速执行
            command = f'ffprobe -v quiet -select_streams v:0 -show_entries format=duration -of csv="p=0" -analyzeduration 1000000 -probesize 1000000 "{file_path}"'

            # 添加超时机制，避免长时间等待
            result = subprocess.run(command, shell=True, capture_output=True, text=True,
                                  encoding='utf-8', errors='ignore', timeout=10)

            if result.returncode == 0 and result.stdout.strip():
                duration_seconds = float(result.stdout.strip())
                hours = int(duration_seconds // 3600)
                minutes = int((duration_seconds % 3600) // 60)
                seconds = int(duration_seconds % 60)
                return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        except subprocess.TimeoutExpired:
            print("获取视频时长超时")
        except Exception as e:
            print(f"获取视频时长失败: {e}")
        return None

    def get_video_frame_count(self, file_path, start_time, end_time):
        '''获取视频片段的总帧数'''
        try:
            # 计算时长（秒）
            start_seconds = self.time_to_seconds(start_time)
            end_seconds = self.time_to_seconds(end_time)
            duration = end_seconds - start_seconds

            # 获取帧率
            command = f'ffprobe -v quiet -select_streams v:0 -show_entries stream=r_frame_rate -of csv="p=0" "{file_path}"'
            result = subprocess.run(command, shell=True, capture_output=True, text=True,
                                  encoding='utf-8', errors='ignore')
            if result.returncode == 0:
                fps_str = result.stdout.strip()
                if '/' in fps_str:
                    num, den = fps_str.split('/')
                    fps = float(num) / float(den)
                else:
                    fps = float(fps_str)

                # 计算总帧数
                total_frames = int(duration * fps)
                return total_frames
        except Exception as e:
            print(f"获取视频帧数失败: {e}")
        return None

    def time_to_seconds(self, time_str):
        '''将时间字符串转换为秒数（支持毫秒）'''
        try:
            if '.' in time_str:
                main_time, milliseconds = time_str.split('.')
                parts = main_time.split(':')
                hours = int(parts[0])
                minutes = int(parts[1])
                seconds = int(parts[2])
                milliseconds = int(milliseconds)
                return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
            else:
                parts = time_str.split(':')
                hours = int(parts[0])
                minutes = int(parts[1])
                seconds = int(parts[2])
                return hours * 3600 + minutes * 60 + seconds
        except:
            return 0

    def check_gpu_support(self):
        '''检查NVIDIA GPU编码器支持情况'''
        gpu_support = {
            'nvidia': False
        }

        try:
            # 只检查NVIDIA GPU支持（用户只有NVIDIA GPU）
            result = subprocess.run('ffmpeg -hide_banner -encoders 2>&1 | findstr nvenc',
                                  shell=True, capture_output=True, text=True,
                                  encoding='utf-8', errors='ignore')
            if result.returncode == 0 and 'h264_nvenc' in result.stdout:
                gpu_support['nvidia'] = True

        except Exception as e:
            print(f"检查NVIDIA GPU支持时出错: {e}")

        return gpu_support

    def load_file(self):
        '''加载视频文件'''
        try:
            filename = filedialog.askopenfilename(
                title="选择视频文件",
                filetypes=[
                    ("视频文件", "*.mp4 *.avi *.mkv *.mov *.wmv *.flv *.webm *.m4v *.3gp *.mpg *.mpeg *.ts *.mts"),
                    ("所有文件", "*.*")
                ]
            )
            
            if filename:
                print(f"选择的文件: {filename}")
                # 清除提示文字
                if DRAG_DROP_AVAILABLE:
                    self.clear_placeholder_text()

                # 更新文本框
                self.Text1.delete(1.0, tk.END)
                self.Text1.insert(tk.END, filename)

                # 显示加载提示
                self.show_loading_status("正在获取视频信息...")

                # 打开视频预览
                if hasattr(self, 'start_preview'):
                    self.start_preview.open_video(filename)
                if hasattr(self, 'end_preview'):
                    self.end_preview.open_video(filename)

                # 在后台线程中获取视频时长，避免界面卡顿
                thread = threading.Thread(target=self.get_video_info_async, args=(filename,))
                thread.daemon = True
                thread.start()

        except Exception as e:
            print(f"加载文件时出错: {e}")
            messagebox.showerror("错误", f"加载文件时出错: {str(e)}")

    def start_progress(self):
        '''启动进度条'''
        print("开始处理...")  # 调试信息
        self.progressbar['value'] = 0
        self.progress_label.configure(text="准备开始处理...")
        
        # 强制更新界面
        self.progressbar.update()
        self.main_frame.update()

    def stop_progress(self):
        '''停止进度条'''
        print("处理完成...")  # 调试信息
        self.progressbar['value'] = 0
        self.progress_label.configure(text="处理完成")

    def update_progress(self, current_frame, total_frames, status=""):
        '''更新进度显示'''
        if total_frames > 0:
            progress = (current_frame / total_frames) * 100
            self.progressbar['value'] = progress

            # 更新标签文字，添加更多详细信息
            label_text = f"{status} - 进度: {progress:.1f}% ({current_frame}/{total_frames}帧)"
            if hasattr(self, 'process_mode'):
                mode_names = ["GPU加速", "CPU快速", "CPU平衡", "CPU高质量", "快速复制"]
                current_mode = mode_names[self.process_mode.get()]
                label_text = f"[{current_mode}] {label_text}"
            
            self.progress_label.configure(text=label_text)

    def check_progress_queue(self):
        '''检查进度队列并更新UI'''
        try:
            while True:
                progress_data = self.progress_queue.get_nowait()
                if progress_data['type'] == 'progress':
                    self.update_progress(progress_data['current'], progress_data['total'], progress_data['status'])
                elif progress_data['type'] == 'status':
                    self.progress_label.config(text=progress_data['message'])
        except queue.Empty:
            pass

        # 继续检查队列
        if hasattr(self, '_progress_check_id'):
            self.progress_label.after(100, self.check_progress_queue)

    def run_ffmpeg_with_progress(self, command, total_frames, status="处理中"):
        '''运行ffmpeg并实时显示进度'''
        try:
            print(f"执行命令: {command}")
            process = subprocess.Popen(command, shell=True, stderr=subprocess.PIPE,
                                     stdout=subprocess.PIPE, universal_newlines=True, bufsize=1,
                                     encoding='utf-8', errors='ignore')

            # 保存当前进程引用
            self.current_process = process
            self.is_processing = True

            frame_pattern = re.compile(r'frame=\s*(\d+)')
            error_output = []

            for line in process.stderr:
                # 检查进程是否被终止
                if not self.is_processing:
                    break

                error_output.append(line)
                print(f"ffmpeg输出: {line.strip()}")  # 调试信息

                # 解析当前帧数
                match = frame_pattern.search(line)
                if match:
                    current_frame = int(match.group(1))
                    # 将进度信息放入队列
                    self.progress_queue.put({
                        'type': 'progress',
                        'current': current_frame,
                        'total': total_frames,
                        'status': status
                    })

            process.wait()

            # 如果失败，保存错误信息
            if process.returncode != 0:
                self.last_error = ''.join(error_output)
                print(f"ffmpeg失败，返回码: {process.returncode}")
                print(f"错误信息: {self.last_error}")

            return process.returncode

        except Exception as e:
            print(f"执行ffmpeg时出错: {e}")
            self.last_error = str(e)
            return 1
        finally:
            # 清理进程引用
            self.current_process = None
            self.is_processing = False

    def split_media(self):
        '''Split media based on start and end times'''
        self.Button2.config(state=tk.DISABLED, text='分割中')  # 设置按钮为不可点击并更改文本
        self.start_progress()  # 启动进度条

        # 启动进度检查
        self._progress_check_id = True
        self.check_progress_queue()

        thread = threading.Thread(target=self._split_media)
        thread.start()

    def _split_media(self):
        '''Internal method to handle the actual media splitting process.'''
        self.is_processing = True  # 设置处理状态

        file_path = self.Text1.get('1.0', tk.END).strip()
        start_time = self.get_start_time()
        end_time = self.get_end_time()
        if file_path and start_time and end_time:
            extension = os.path.splitext(file_path)[1].lower()
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            output_dir = os.path.dirname(file_path)

            # 根据处理模式生成输出文件名
            mode_suffixes = {
                0: "gpu",
                1: "cpu_fast",
                2: "cpu_balanced",
                3: "cpu_quality",
                4: "fast_copy"
            }
            mode = self.process_mode.get()
            suffix = mode_suffixes.get(mode, "output")
            output_file = os.path.join(output_dir, f"{base_name}_{suffix}{extension}")

            # 获取视频帧数用于进度显示
            self.progress_queue.put({
                'type': 'status',
                'message': '正在分析视频信息...'
            })

            self.total_frames = self.get_video_frame_count(file_path, start_time, end_time)
            if not self.total_frames:
                self.total_frames = 1000  # 默认值，避免除零错误

            # 检查GPU支持情况（如果选择了GPU模式）
            if mode == 0:
                gpu_support = self.check_gpu_support()
                if not gpu_support['nvidia']:
                    # 如果没有GPU支持，自动切换到CPU平衡模式
                    self.process_mode.set(2)
                    mode = 2
                    output_file = os.path.join(output_dir, f"{base_name}_cpu_balanced{extension}")
                    messagebox.showinfo("提示", "未检测到NVIDIA GPU，已自动切换到CPU平衡模式。")

            # 生成处理命令
            command = self.get_ffmpeg_command(file_path, start_time, end_time, output_file)

            # 执行命令
            mode_names = ["GPU加速", "CPU快速", "CPU平衡", "CPU高质量", "快速复制"]
            mode_name = mode_names[mode]

            print(f"使用{mode_name}模式处理: {command}")
            self.progress_queue.put({
                'type': 'status',
                'message': f'使用{mode_name}模式处理中...'
            })

            returncode = self.run_ffmpeg_with_progress(command, self.total_frames, mode_name)
            result = type('obj', (object,), {'returncode': returncode, 'stderr': self.last_error})()

            # 停止进度检查
            self._progress_check_id = False

            if result and result.returncode == 0:
                self.progress_queue.put({
                    'type': 'status',
                    'message': '视频分割完成！'
                })
                time.sleep(0.5)  # 让用户看到完成消息
                self.stop_progress()  # 停止进度条
                self.Button2.config(state=tk.NORMAL, text='开始分割')  # 恢复按钮状态
                messagebox.showinfo("视频分割", "视频分割完成！")
            else:
                self.stop_progress()  # 停止进度条
                self.Button2.config(state=tk.NORMAL, text='开始分割')  # 恢复按钮状态
                error_msg = "分割过程中发生错误。"
                if hasattr(result, 'stderr') and result.stderr:
                    if isinstance(result.stderr, bytes):
                        error_msg += f"\n{result.stderr.decode('utf-8', errors='ignore')}"
                    else:
                        error_msg += f"\n{result.stderr}"
                elif self.last_error:
                    error_msg += f"\n{self.last_error}"
                messagebox.showerror("错误", error_msg)
        else:
            self._progress_check_id = False
            self.stop_progress()  # 停止进度条
            self.Button2.config(state=tk.NORMAL, text='开始分割')  # 恢复按钮状态
            messagebox.showerror("错误", "请正确填写所有字段。")

        # 重置处理状态
        self.is_processing = False
        self.current_process = None

    def on_closing(self):
        '''处理窗口关闭事件'''
        if self.is_processing and self.current_process:
            # 如果正在处理，询问用户是否确认关闭
            import tkinter.messagebox as msgbox
            result = msgbox.askyesno("确认关闭",
                                   "视频分割正在进行中，关闭程序将终止当前任务。\n确定要关闭吗？")
            if result:
                self.terminate_current_process()
                # 关闭视频预览
                if hasattr(self, 'start_preview'):
                    self.start_preview.close()
                if hasattr(self, 'end_preview'):
                    self.end_preview.close()
                self.destroy_window()
            # 如果用户选择"否"，则不关闭窗口
        else:
            # 关闭视频预览
            if hasattr(self, 'start_preview'):
                self.start_preview.close()
            if hasattr(self, 'end_preview'):
                self.end_preview.close()
            # 没有正在进行的任务，直接关闭
            self.destroy_window()

    def terminate_current_process(self):
        '''终止当前的ffmpeg进程'''
        if self.current_process:
            try:
                # 终止进程
                self.current_process.terminate()
                # 等待进程结束，最多等待3秒
                try:
                    self.current_process.wait(timeout=3)
                except subprocess.TimeoutExpired:
                    # 如果3秒后还没结束，强制杀死进程
                    self.current_process.kill()
                    self.current_process.wait()
                print("已终止ffmpeg进程")
            except Exception as e:
                print(f"终止进程时出错: {e}")
            finally:
                self.current_process = None
                self.is_processing = False

    def destroy_window(self):
        '''销毁窗口'''
        try:
            # 获取顶级窗口
            top_window = self.main_frame.winfo_toplevel()
            top_window.destroy()
        except:
            pass

    def update_mode_info(self, *args):
        '''更新处理模式说明文字'''
        mode = self.process_mode.get()
        if mode in self.MODE_CONFIG:
            info_text = f"{self.MODE_CONFIG[mode]['icon']} {self.MODE_CONFIG[mode]['info']}"
            self.info_label.configure(text=info_text)

    def get_cpu_threads(self):
        '''获取CPU线程数'''
        try:
            import multiprocessing
            return multiprocessing.cpu_count()
        except:
            return 4  # 默认值

    def get_ffmpeg_command(self, file_path, start_time, end_time, output_file):
        '''根据处理模式生成ffmpeg命令'''
        mode = self.process_mode.get()
        threads = self.get_cpu_threads()
        
        # 基础命令部分
        base_cmd = f'ffmpeg -y -i "{file_path}" -ss {start_time} -to {end_time}'
        
        if mode == 0:  # GPU模式
            return f'{base_cmd} -c:v h264_nvenc -preset fast -c:a aac -avoid_negative_ts make_zero "{output_file}"'
        
        elif mode == 1:  # CPU快速模式
            return f'{base_cmd} -c:v libx264 -preset ultrafast -crf 23 -threads {threads} ' \
                   f'-c:a aac -avoid_negative_ts make_zero "{output_file}"'
        
        elif mode == 2:  # CPU平衡模式
            return f'{base_cmd} -c:v libx264 -preset medium -crf 22 -threads {threads} ' \
                   f'-profile:v high -pix_fmt yuv420p -c:a aac -b:a 192k ' \
                   f'-avoid_negative_ts make_zero "{output_file}"'
        
        elif mode == 3:  # CPU高质量模式
            return f'{base_cmd} -c:v libx264 -preset medium -crf 18 -threads {threads} ' \
                   f'-profile:v high -pix_fmt yuv420p -x264-params "ref=6:deblock=1,1" ' \
                   f'-c:a aac -b:a 256k -map_metadata 0 ' \
                   f'-avoid_negative_ts make_zero "{output_file}"'
        
        else:  # 快速复制模式
            return f'{base_cmd} -c copy -avoid_negative_ts make_zero "{output_file}"'


if __name__ == '__main__':
    vp_start_gui()