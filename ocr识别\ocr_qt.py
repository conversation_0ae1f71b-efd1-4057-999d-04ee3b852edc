import sys
import os
import time
import logging
import threading
import hashlib
import json
import math
from pathlib import Path

# 抑制Qt的QPainter警告信息，只保留真正的错误和警告
os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*.debug=false;qt.widgets.*.debug=false'

from PySide6.QtCore import (Qt, QThread, Signal, Slot, QObject, QEvent, QTimer, QPointF, QPropertyAnimation, QEasingCurve, QLoggingCategory, qInstallMessageHandler, QtMsgType)
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QTextEdit, QLabel, QFileDialog, QSplitter, QGroupBox, QRadioButton,
    QScrollArea, QMenuBar, QMessageBox, QButtonGroup,
    QDialog, QGraphicsDropShadowEffect, QProgressBar, QGraphicsOpacityEffect
)
from PySide6.QtGui import (QPixmap, QPainter, QGuiApplication, QColor,
                         QAction, QCursor, QKeySequence, QIcon, QPixmapCache, QFont)
from PIL import Image, ImageGrab, ImageEnhance, ImageFilter
import pyperclip

# 自定义Qt消息处理器，过滤不必要的警告
def qt_message_handler(msg_type, context, message):
    """自定义Qt消息处理器，过滤不必要的Qt警告"""
    # 过滤掉不必要的警告信息
    filtered_messages = [
        "QPainter::begin: A paint device can only be painted by one painter at a time",
        "QPainter::translate: Painter not active",
        "QPainter::worldTransform: Painter not active",
        "QWidgetEffectSourcePrivate::pixmap: Painter not active",
        "QPainter::setWorldTransform: Painter not active",
        "QPainter::end: Painter not active",
        "Unknown property transform",  # CSS transform属性警告
        "QWidget::setMinimumSize: The smallest allowed size is",
        "QWidget::setMaximumSize: The largest allowed size is"
    ]

    # 只显示真正的错误和重要警告
    if msg_type == QtMsgType.QtCriticalMsg or msg_type == QtMsgType.QtFatalMsg:
        print(f"Qt Critical/Fatal: {message}")
    elif msg_type == QtMsgType.QtWarningMsg:
        # 检查是否是需要过滤的警告
        if not any(filtered_msg in message for filtered_msg in filtered_messages):
            print(f"Qt Warning: {message}")
    # 忽略Debug和Info消息

# 安装自定义消息处理器
qInstallMessageHandler(qt_message_handler)

# --- PaddleOCR setup ---
# It's better to handle this within the processor to keep UI code clean
# but for simplicity, we set it here.
os.environ['GLOG_minloglevel'] = '2'  # Suppress PaddlePaddle logging
try:
    from paddleocr import PaddleOCR
    import paddle
    PADDLE_AVAILABLE = True
except ImportError:
    PADDLE_AVAILABLE = False
    PaddleOCR = None
    paddle = None

# --- Enhanced Cache Management ---
class CacheManager:
    """优化的缓存管理器，防止内存泄漏"""
    def __init__(self, cache_dir="cache", max_ocr_cache=500, max_pixmap_cache_mb=50):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.ocr_cache_file = self.cache_dir / "ocr_cache.json"
        self.ocr_cache = self._load_ocr_cache()
        self.max_ocr_cache = max_ocr_cache

        # 设置更合理的QPixmapCache大小
        QPixmapCache.setCacheLimit(max_pixmap_cache_mb * 1024)

        # 添加缓存统计
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0
        }

    def _load_ocr_cache(self):
        """加载OCR缓存"""
        if self.ocr_cache_file.exists():
            try:
                with open(self.ocr_cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return {}
        return {}

    def _save_ocr_cache(self):
        """保存OCR缓存"""
        try:
            with open(self.ocr_cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.ocr_cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logging.warning(f"保存OCR缓存失败: {e}")

    def get_image_hash(self, image_data):
        """计算图片的哈希值"""
        return hashlib.md5(image_data).hexdigest()

    def get_cached_ocr_result(self, image_hash):
        """获取缓存的OCR结果，并更新访问统计"""
        cached_data = self.ocr_cache.get(image_hash)
        if cached_data:
            # 更新访问统计
            cached_data['access_count'] = cached_data.get('access_count', 0) + 1
            cached_data['last_access'] = time.time()
            self.cache_stats['hits'] += 1
            return cached_data
        else:
            self.cache_stats['misses'] += 1
            return None

    def cache_ocr_result(self, image_hash, result):
        """缓存OCR结果，带智能清理"""
        self.ocr_cache[image_hash] = {
            'result': result,
            'timestamp': time.time(),
            'access_count': 1
        }

        # 智能缓存清理：优先删除访问次数少且时间久的条目
        if len(self.ocr_cache) > self.max_ocr_cache:
            # 计算每个条目的权重（访问次数 * 时间衰减因子）
            current_time = time.time()
            scored_items = []

            for key, data in self.ocr_cache.items():
                age_hours = (current_time - data['timestamp']) / 3600
                # 时间越久权重越低，访问次数越多权重越高
                score = data.get('access_count', 1) / (1 + age_hours * 0.1)
                scored_items.append((score, key))

            # 删除权重最低的条目
            scored_items.sort()
            items_to_remove = len(self.ocr_cache) - self.max_ocr_cache + 50  # 多删除一些避免频繁清理
            for i in range(min(items_to_remove, len(scored_items))):
                del self.ocr_cache[scored_items[i][1]]
                self.cache_stats['evictions'] += 1

        self._save_ocr_cache()

    def get_cached_pixmap(self, key):
        """从QPixmapCache获取缓存的图片"""
        pixmap = QPixmap()
        if QPixmapCache.find(key, pixmap):
            return pixmap
        return None

    def cache_pixmap(self, key, pixmap):
        """缓存图片到QPixmapCache"""
        QPixmapCache.insert(key, pixmap)

# --- Image Processing Utilities ---
class ImageProcessor:
    """图片处理工具类"""

    @staticmethod
    def enhance_image_for_ocr(image_data):
        """为OCR优化图片"""
        try:
            # 将字节数据转换为PIL图片
            import io
            image = Image.open(io.BytesIO(image_data))

            # 转换为RGB模式
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # 增强对比度
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.2)

            # 增强锐度
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.1)

            # 轻微降噪
            image = image.filter(ImageFilter.MedianFilter(size=3))

            # 转换回字节数据
            output = io.BytesIO()
            image.save(output, format='PNG', optimize=True)
            return output.getvalue()
        except Exception as e:
            logging.warning(f"图片预处理失败: {e}")
            return image_data

    @staticmethod
    def resize_image_if_large(image_data, max_size=2048):
        """如果图片太大则调整大小"""
        try:
            import io
            image = Image.open(io.BytesIO(image_data))

            # 检查图片大小
            if max(image.size) > max_size:
                # 计算新尺寸
                ratio = max_size / max(image.size)
                new_size = tuple(int(dim * ratio) for dim in image.size)
                image = image.resize(new_size, Image.Resampling.LANCZOS)

                # 转换回字节数据
                output = io.BytesIO()
                image.save(output, format='PNG', optimize=True)
                return output.getvalue()

            return image_data
        except Exception as e:
            logging.warning(f"图片大小调整失败: {e}")
            return image_data

# --- Status Animation using StatusBar ---
# 使用状态栏动画，避免覆盖图片的问题

# --- Enhanced Loading Dialog ---
class QLoadingDialog(QDialog):
    """显示长时间操作进度的现代化模态对话框"""
    def __init__(self, parent=None, message="正在加载...", show_progress=False):
        super().__init__(parent)
        self.setWindowTitle("请稍候")
        self.setModal(True)
        self.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # 创建主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 创建内容容器
        self.container = QWidget(self)
        self.container.setObjectName("container")
        container_layout = QVBoxLayout(self.container)
        container_layout.setContentsMargins(40, 40, 40, 40)
        container_layout.setSpacing(25)

        # 添加加载动画
        self.spinner = QLabel()
        self.spinner.setFixedSize(64, 64)
        self.spinner.setObjectName("spinner")
        container_layout.addWidget(self.spinner, 0, Qt.AlignCenter)

        # 添加加载提示文本
        self.label = QLabel(message)
        self.label.setAlignment(Qt.AlignCenter)
        self.label.setObjectName("message")
        self.label.setWordWrap(True)
        container_layout.addWidget(self.label)

        # 可选的进度条
        self.progress_bar = None
        if show_progress:
            self.progress_bar = QProgressBar()
            self.progress_bar.setObjectName("progressBar")
            self.progress_bar.setRange(0, 0)  # 无限进度条
            container_layout.addWidget(self.progress_bar)

        layout.addWidget(self.container)
        self.setFixedSize(450, 250 if show_progress else 220)

        # 现代化样式
        self.setStyleSheet("""
            QDialog {
                background-color: rgba(0, 0, 0, 70);
            }
            QWidget#container {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-radius: 15px;
                border: 1px solid rgba(255, 255, 255, 0.8);
            }
            QLabel#message {
                font-family: "Microsoft YaHei", "Segoe UI";
                font-size: 15px;
                font-weight: 500;
                color: #2c3e50;
                padding: 10px;
                background: transparent;
            }
            QLabel#spinner {
                background: transparent;
            }
            QProgressBar#progressBar {
                border: none;
                border-radius: 8px;
                background-color: #e9ecef;
                height: 8px;
                text-align: center;
            }
            QProgressBar#progressBar::chunk {
                border-radius: 8px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:1 #2196F3);
            }
        """)

        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 5)
        self.container.setGraphicsEffect(shadow)

        # 添加淡入动画
        self.opacity_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.opacity_effect)
        self.fade_in_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_in_animation.setDuration(300)
        self.fade_in_animation.setStartValue(0.0)
        self.fade_in_animation.setEndValue(1.0)
        self.fade_in_animation.setEasingCurve(QEasingCurve.OutCubic)

        self.start_animation()

    def start_animation(self):
        """启动现代化加载动画"""
        # 创建旋转动画
        self.rotation = 0
        self.animation_timer = QTimer(self)
        self.animation_timer.timeout.connect(self.update_rotation)
        self.animation_timer.start(16)  # 60 FPS for smooth animation

        # 启动淡入动画
        self.fade_in_animation.start()

    def update_rotation(self):
        """更新旋转角度 - 现代化设计"""
        self.rotation = (self.rotation + 6) % 360

        # 创建现代化的加载图标
        size = 64
        pixmap = QPixmap(size, size)
        if pixmap.isNull():
            logging.warning("无法创建有效的QPixmap")
            return

        pixmap.fill(Qt.transparent)

        # 使用try-except保护QPainter操作，防止异常
        try:
            painter = QPainter()
            if not painter.begin(pixmap):  # 显式调用begin并检查返回值
                logging.warning("QPainter无法开始绘制")
                return

            painter.setRenderHint(QPainter.Antialiasing)

            center = QPointF(size/2, size/2)
            radius = size/2 - 8

            # 绘制多个圆点组成的加载动画
            for i in range(8):
                angle = i * 45 + self.rotation
                x = center.x() + radius * 0.7 * math.cos(math.radians(angle))
                y = center.y() + radius * 0.7 * math.sin(math.radians(angle))

                # 计算透明度（创建拖尾效果）
                alpha = max(0.2, 1.0 - (i * 0.12))

                # 设置颜色
                color = QColor("#1a73e8")
                color.setAlphaF(alpha)
                painter.setBrush(color)
                painter.setPen(Qt.NoPen)

                # 绘制圆点
                dot_size = 6 - i * 0.3
                painter.drawEllipse(QPointF(x, y), dot_size, dot_size)

            painter.end()  # 确保结束绘制
            self.spinner.setPixmap(pixmap)
        except Exception as e:
            logging.warning(f"绘制加载动画时出错: {e}")
            if painter.isActive():
                painter.end()  # 确保在异常情况下也结束绘制

    def update_message(self, message):
        """更新加载消息"""
        self.label.setText(message)

    def set_progress(self, value):
        """设置进度值"""
        if self.progress_bar:
            if value < 0:
                self.progress_bar.setRange(0, 0)  # 无限进度
            else:
                self.progress_bar.setRange(0, 100)
                self.progress_bar.setValue(value)

    def showEvent(self, event):
        """重写showEvent以确保对话框在屏幕中央显示"""
        super().showEvent(event)

        # 获取屏幕几何信息
        screen = QGuiApplication.primaryScreen().geometry()

        # 如果有父窗口，相对于父窗口居中
        if self.parent():
            parent_rect = self.parent().geometry()
            x = parent_rect.x() + (parent_rect.width() - self.width()) // 2
            y = parent_rect.y() + (parent_rect.height() - self.height()) // 2

            # 确保对话框不会超出屏幕边界
            x = max(0, min(x, screen.width() - self.width()))
            y = max(0, min(y, screen.height() - self.height()))
        else:
            # 没有父窗口时，在屏幕中央显示
            x = (screen.width() - self.width()) // 2
            y = (screen.height() - self.height()) // 2

        self.move(x, y)

    def closeEvent(self, event):
        """重写closeEvent以停止动画"""
        if hasattr(self, 'animation_timer'):
            self.animation_timer.stop()
        if hasattr(self, 'fade_in_animation'):
            self.fade_in_animation.stop()
        super().closeEvent(event)

# --- Enhanced Zoomable Image Label ---
class ZoomableImageLabel(QLabel):
    def __init__(self, cache_manager=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.setScaledContents(False)
        self.setAlignment(Qt.AlignCenter)
        self.setCursor(Qt.OpenHandCursor)
        self._pixmap = QPixmap()
        self.scale_factor = 1.0
        self.scroll_area = None
        self.is_dragging = False
        self.last_mouse_pos = None
        self.cache_manager = cache_manager
        self.image_hash = None
        self.scaled_pixmap_cache = {}

        # 设置焦点策略，使其能够接收键盘事件
        self.setFocusPolicy(Qt.ClickFocus)

        # 存储主窗口引用，用于调用粘贴功能
        self.main_window = None

        # 设置现代化的默认样式
        self.setStyleSheet("""
            QLabel {
                color: #6c757d;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 2px dashed #dee2e6;
                border-radius: 12px;
                font-family: "Microsoft YaHei", "Segoe UI";
                font-size: 14px;
                padding: 20px;
            }
        """)

        # 创建现代化的默认提示文本
        self.default_text = ("📁 拖拽、粘贴或选择图片\n\n"
                             "✨ 支持的操作：\n"
                             "• 🖱️ 拖放图片到此处\n"
                             "• 📋 Ctrl+V 粘贴剪贴板图片\n"
                             "• 📂 点击\"选择图片\"按钮\n\n"
                             "🔍 图片查看功能：\n"
                             "• 🖱️ 滚轮缩放图片\n"
                             "• ✋ 拖拽平移视图\n"
                             "• 🎯 双击重置缩放")
        self.setText(self.default_text)

    def set_scroll_area(self, scroll_area):
        self.scroll_area = scroll_area

    def set_main_window(self, main_window):
        """设置主窗口引用，用于调用粘贴功能"""
        self.main_window = main_window

    def setPixmap(self, pixmap, image_hash=None):
        """设置新的图片并清除默认文本，支持缓存"""
        self._pixmap = pixmap
        self.image_hash = image_hash
        self.setText("")
        self.setStyleSheet("")
        self.scaled_pixmap_cache.clear()  # 清除旧的缩放缓存
        self.update_scaled_pixmap()

    def clearPixmap(self):
        """清除图片并恢复默认文本"""
        self._pixmap = QPixmap()
        self.image_hash = None
        self.scaled_pixmap_cache.clear()
        self.setText(self.default_text)
        self.setStyleSheet("""
            QLabel {
                color: #6c757d;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 2px dashed #dee2e6;
                border-radius: 12px;
                font-family: "Microsoft YaHei", "Segoe UI";
                font-size: 14px;
                padding: 20px;
            }
        """)

    def update_scaled_pixmap(self):
        """更新缩放后的图片，使用优化的缓存管理"""
        if not self._pixmap.isNull():
            # 检查缓存
            cache_key = f"{self.scale_factor:.3f}"
            if cache_key in self.scaled_pixmap_cache:
                scaled_pixmap = self.scaled_pixmap_cache[cache_key]
            else:
                # 生成新的缩放图片
                size = self._pixmap.size() * self.scale_factor
                scaled_pixmap = self._pixmap.scaled(
                    size, Qt.KeepAspectRatio, Qt.SmoothTransformation
                )

                # 智能缓存管理：限制缓存大小并优化内存使用
                max_cache_size = 8  # 减少缓存大小
                if len(self.scaled_pixmap_cache) >= max_cache_size:
                    # 删除所有缓存项，重新开始（避免复杂的LRU逻辑）
                    self.scaled_pixmap_cache.clear()

                # 只缓存合理大小的图片（避免缓存过大的图片）
                pixmap_size_mb = (scaled_pixmap.width() * scaled_pixmap.height() * 4) / (1024 * 1024)
                if pixmap_size_mb < 10:  # 只缓存小于10MB的图片
                    self.scaled_pixmap_cache[cache_key] = scaled_pixmap

            super().setPixmap(scaled_pixmap)
            self.resize(scaled_pixmap.size())

    def wheelEvent(self, event):
        """改进的滚轮缩放事件"""
        if not self._pixmap.isNull():
            # 计算缩放因子
            zoom_in = event.angleDelta().y() > 0
            zoom_factor = 1.15 if zoom_in else 1/1.15

            # 限制缩放范围
            new_scale = self.scale_factor * zoom_factor
            if 0.1 <= new_scale <= 10.0:
                self.scale_factor = new_scale
                self.update_scaled_pixmap()

    def mousePressEvent(self, event):
        """改进的鼠标按下事件"""
        # 获取焦点以接收键盘事件
        self.setFocus()

        if event.button() == Qt.LeftButton and not self._pixmap.isNull():
            self.is_dragging = True
            self.last_mouse_pos = event.pos()
            self.setCursor(Qt.ClosedHandCursor)
            # 添加视觉反馈
            self.setStyleSheet("border: 2px solid #1a73e8;")

    def mouseMoveEvent(self, event):
        """改进的鼠标移动事件"""
        if self.is_dragging and self.scroll_area:
            delta = event.pos() - self.last_mouse_pos

            h_bar = self.scroll_area.horizontalScrollBar()
            v_bar = self.scroll_area.verticalScrollBar()

            # 平滑滚动
            h_bar.setValue(h_bar.value() - delta.x())
            v_bar.setValue(v_bar.value() - delta.y())

            self.last_mouse_pos = event.pos()

    def mouseReleaseEvent(self, event):
        """改进的鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.is_dragging = False
            self.setCursor(Qt.OpenHandCursor)
            # 移除视觉反馈
            self.setStyleSheet("")

    def mouseDoubleClickEvent(self, event):
        """双击重置缩放"""
        super().mouseDoubleClickEvent(event)
        if not self._pixmap.isNull():
            self.scale_factor = 1.0
            self.update_scaled_pixmap()

    def keyPressEvent(self, event):
        """处理键盘事件，特别是粘贴快捷键"""
        if event.matches(QKeySequence.Paste) and self.main_window:
            # 调用主窗口的粘贴功能
            self.main_window.paste_image()
            event.accept()
        else:
            super().keyPressEvent(event)

            
# --- OCR Processor (Adapted for Qt) ---

class OcrWorker(QObject):
    """OCR处理工作线程"""
    finished = Signal(bool, str)  # Success/Fail, Message/Result

    def __init__(self, processor, image_path=None, image_data=None, device_type='auto', ocr_version='PP-OCRv4'):
        super().__init__()
        self.processor = processor
        self.image_path = image_path
        self.image_data = image_data
        self.device_type = device_type
        self.ocr_version = ocr_version
        self.processing_time = 0

    @Slot()
    def run(self):
        """运行初始化或处理任务"""
        start_time = time.time()
        try:
            if self.image_path:
                result_text = self.processor.process_image(self.image_path)
                self.processing_time = time.time() - start_time
                self.finished.emit(True, result_text)
            elif self.image_data:
                result_text = self.processor.process_image_data(self.image_data)
                self.processing_time = time.time() - start_time
                self.finished.emit(True, result_text)
            else:
                message = self.processor.initialize_ocr(self.device_type, self.ocr_version)
                self.processing_time = time.time() - start_time
                self.finished.emit(True, message)
        except Exception as e:
            self.processing_time = time.time() - start_time
            self.finished.emit(False, str(e))


class OCRProcessor:
    """处理OCR引擎和识别任务的优化版本"""
    def __init__(self, cache_manager=None):
        self.ocr = None
        self.is_initialized = False
        self.device_type = 'auto'
        self.cache_manager = cache_manager or CacheManager()
        self.logger = logging.getLogger(__name__)
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

        # 性能统计
        self.stats = {
            'total_processed': 0,
            'cache_hits': 0,
            'total_time': 0.0
        }

    def check_gpu_support(self):
        """Check if a CUDA-enabled GPU is available."""
        if not PADDLE_AVAILABLE:
            return False
        try:
            return paddle.device.is_compiled_with_cuda() and paddle.device.cuda.device_count() > 0
        except Exception as e:
            self.logger.warning(f"Error checking GPU support: {e}")
            return False

    def initialize_ocr(self, device_type='auto', ocr_version='PP-OCRv4'):
        """
        Initializes the OCR engine with specified device and model version.
        Returns a status message.
        """
        if not PADDLE_AVAILABLE:
            raise ImportError("PaddleOCR or PaddlePaddle is not installed.")

        init_start_time = time.time()
        self.device_type = device_type
        self.ocr_version = ocr_version
        use_gpu = False

        if device_type == 'auto':
            if self.check_gpu_support():
                use_gpu = True
                mode_name = "GPU (Auto-detected)"
            else:
                use_gpu = False
                mode_name = "CPU (Auto-detected)"
        elif device_type == 'gpu':
            if not self.check_gpu_support():
                raise RuntimeError("GPU support requested, but not available or drivers are incorrect.")
            use_gpu = True
            mode_name = "GPU (User-selected)"
        else: # cpu
            use_gpu = False
            mode_name = "CPU (User-selected)"

        self.logger.info(f"Initializing OCR with {mode_name} using {ocr_version}...")
        try:
            # Re-initialize to apply new settings with specified OCR version
            self.ocr = PaddleOCR(
                use_angle_cls=True,
                lang='ch',
                use_gpu=use_gpu,
                show_log=False,
                ocr_version=ocr_version
            )
            self.is_initialized = True
            elapsed_time = time.time() - init_start_time
            message = f"OCR 引擎成功初始化 {mode_name} ({ocr_version}). 时间: {elapsed_time:.2f}s"
            self.logger.info(message)
            return message
        except Exception as e:
            self.is_initialized = False
            self.ocr = None
            error_message = f"初始化 OCR 失败 {mode_name} ({ocr_version}): {e}"
            self.logger.error(error_message)
            # Try CPU as a fallback if auto/gpu failed
            if use_gpu and device_type != 'cpu':
                self.logger.info("回滚到 CPU 初始化...")
                return self.initialize_ocr(device_type='cpu', ocr_version=ocr_version)
            raise RuntimeError(error_message)

    def process_image(self, image_path):
        """
        Processes a single image file and returns the OCR text.
        """
        if not self.is_initialized or not self.ocr:
            raise RuntimeError("OCR engine is not initialized.")
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Image file not found: {image_path}")

        self.logger.info(f"Processing image: {image_path}")
        start_time = time.time()

        # The result from PaddleOCR is a list of lists.
        result = self.ocr.ocr(image_path, cls=True)

        processing_time = time.time() - start_time
        self.logger.info(f"Image processing finished in {processing_time:.2f}s")

        if not result or not result[0]:
            return "No text detected."

        # Extract text from the result structure
        lines = [line[1][0] for line in result[0]]
        return "\n".join(lines)

    def process_image_data(self, image_data):
        """处理内存中的图片数据，支持缓存和预处理"""
        if not self.is_initialized or not self.ocr:
            raise RuntimeError("OCR 引擎未初始化。")

        start_time = time.time()
        self.stats['total_processed'] += 1

        # 计算图片哈希值
        image_hash = self.cache_manager.get_image_hash(image_data)

        # 检查缓存
        cached_result = self.cache_manager.get_cached_ocr_result(image_hash)
        if cached_result:
            self.stats['cache_hits'] += 1
            self.logger.info(f"使用缓存结果 (命中率: {self.stats['cache_hits']/self.stats['total_processed']:.1%})")
            return cached_result['result']

        self.logger.info("处理新图片数据")

        # 图片预处理优化
        try:
            # 调整图片大小（如果太大）
            processed_data = ImageProcessor.resize_image_if_large(image_data)
            # 增强图片质量
            processed_data = ImageProcessor.enhance_image_for_ocr(processed_data)
        except Exception as e:
            self.logger.warning(f"图片预处理失败，使用原图: {e}")
            processed_data = image_data

        # 使用临时文件处理，确保清理
        import tempfile
        temp_file_path = None
        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as temp_file:
                temp_file.write(processed_data)
                temp_file_path = temp_file.name

            result = self.ocr.ocr(temp_file_path, cls=True)
        finally:
            # 确保临时文件被删除
            if temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                except OSError as e:
                    self.logger.warning(f"清理临时文件失败: {e}")

        processing_time = time.time() - start_time
        self.stats['total_time'] += processing_time
        self.logger.info(f"图像处理完成 {processing_time:.2f}s (平均: {self.stats['total_time']/self.stats['total_processed']:.2f}s)")

        if not result or not result[0]:
            result_text = "未检测到文本。"
        else:
            # 提取文本
            lines = [line[1][0] for line in result[0]]
            result_text = "\n".join(lines)

        # 缓存结果
        self.cache_manager.cache_ocr_result(image_hash, result_text)

        return result_text

# --- Enhanced Modern Style Sheet ---
STYLE_SHEET = """
QWidget {
    font-family: 'Microsoft YaHei', 'Segoe UI', 'SF Pro Display', Arial, sans-serif;
    font-size: 10pt;
    color: #2c3e50;
    selection-background-color: #e3f2fd;
}

QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
}

QGroupBox {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 15px;
    margin: 5px;
    font-weight: 600;
    color: #495057;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 8px 0 8px;
    color: #1a73e8;
    font-weight: bold;
}

QRadioButton {
    background-color: transparent;
    padding: 6px 10px;
    border-radius: 6px;
    spacing: 5px;
    min-width: 80px;
    font-weight: 500;
}

QRadioButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
    border: 1px solid #dee2e6;
}

QRadioButton:checked {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #e3f2fd, stop:1 #bbdefb);
    color: #1565c0;
    border: 1px solid #2196f3;
}

QRadioButton::indicator {
    width: 20px;
    height: 20px;
    border-radius: 10px;
    border: 2px solid #bdc3c7;
    margin-right: 8px;
    background-color: white;
}

QRadioButton::indicator:checked {
    background: qradialgradient(cx:0.5, cy:0.5, radius:0.5,
        stop:0 #1a73e8, stop:0.6 #1a73e8, stop:0.7 white, stop:1 white);
    border: 2px solid #1a73e8;
}

QRadioButton:disabled {
    color: #adb5bd;
}

QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1a73e8, stop:1 #1557b0);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 5px;
    font-weight: 500;
    min-width: 70px;
    min-height: 14px;
    font-size: 9pt;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1557b0, stop:1 #0d47a1);
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #0d47a1, stop:1 #1557b0);
}

QPushButton:disabled {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #e9ecef, stop:1 #dee2e6);
    color: #adb5bd;
    border: 1px solid #dee2e6;
}

QPushButton:disabled:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #e9ecef, stop:1 #dee2e6);
    color: #adb5bd;
}

QTextEdit {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 16px;
    selection-background-color: #e3f2fd;
    selection-color: #1565c0;
    font-size: 11pt;
    line-height: 1.4;
}

QTextEdit:focus {
    border: 2px solid #2196f3;
    background-color: white;
}

QScrollArea {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
}

QScrollBar:vertical {
    border: none;
    background: rgba(248, 249, 250, 0.8);
    width: 12px;
    margin: 0px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #bdc3c7, stop:1 #95a5a6);
    min-height: 30px;
    border-radius: 6px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #95a5a6, stop:1 #7f8c8d);
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background: rgba(248, 249, 250, 0.8);
    height: 12px;
    margin: 0px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #bdc3c7, stop:1 #95a5a6);
    min-width: 30px;
    border-radius: 6px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #95a5a6, stop:1 #7f8c8d);
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

QSplitter::handle {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #dee2e6, stop:0.5 #adb5bd, stop:1 #dee2e6);
}

QSplitter::handle:horizontal {
    width: 2px;
}

QSplitter::handle:vertical {
    height: 2px;
}

QSplitter::handle:hover {
    background: #2196f3;
}

QMenuBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
    border-bottom: 1px solid #dee2e6;
    padding: 4px;
}

QMenuBar::item {
    padding: 8px 16px;
    color: #495057;
    border-radius: 6px;
    margin: 2px;
    font-weight: 500;
}

QMenuBar::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #e3f2fd, stop:1 #bbdefb);
    color: #1565c0;
}

QMenu {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 6px;
}

QMenu::item {
    padding: 8px 32px 8px 16px;
    border-radius: 6px;
    margin: 2px;
    color: #495057;
}

QMenu::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #e3f2fd, stop:1 #bbdefb);
    color: #1565c0;
}

QLabel {
    color: #495057;
}

QStatusBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
    border-top: 1px solid #dee2e6;
    color: #6c757d;
    font-size: 9pt;
}

QStatusBar::item {
    border: none;
}

QProgressBar {
    border: none;
    border-radius: 8px;
    background-color: #e9ecef;
    height: 16px;
    text-align: center;
    color: #495057;
    font-weight: 600;
}

QProgressBar::chunk {
    border-radius: 8px;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #4CAF50, stop:0.5 #2196F3, stop:1 #9C27B0);
}
"""

class OCRApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("AI智能OCR识别工具 - 高性能版")
        self.setGeometry(100, 100, 1200, 750)  # 适中的窗口大小
        self.setMinimumSize(900, 600)  # 适合小屏幕的最小尺寸

        # 初始化缓存管理器
        self.cache_manager = CacheManager()
        self.ocr_processor = OCRProcessor(self.cache_manager)
        self.current_image_path = None
        self.current_image_data = None
        self.current_image_hash = None
        self.loading_dialog = None
        self.thread = None
        self.worker = None
        self.is_first_init = True  # 标记是否首次初始化

        # 性能监控
        self.performance_stats = {
            'session_start': time.time(),
            'images_processed': 0,
            'total_processing_time': 0.0
        }

        self._create_widgets()
        self._create_layouts()
        self._create_connections()
        self._create_menu()
        self._setup_shortcuts()
        self.setAcceptDrops(True)
        self.setStyleSheet(STYLE_SHEET)

        # 显示初始化状态
        self._show_initialization_message()
        self._disable_controls_during_init()

        # 异步初始化OCR
        QTimer.singleShot(500, self.reinitialize_ocr)

        self.center_window()

    def center_window(self):
        """将主窗口居中显示并根据屏幕大小智能调整"""
        screen = QGuiApplication.primaryScreen().geometry()

        # 根据屏幕大小智能调整窗口尺寸
        screen_width = screen.width()
        screen_height = screen.height()

        # 计算合适的窗口大小（屏幕的80%，但不超过设定的最大值）
        max_width = min(1200, int(screen_width * 0.8))
        max_height = min(750, int(screen_height * 0.8))

        # 确保不小于最小尺寸
        window_width = max(900, max_width)
        window_height = max(600, max_height)

        # 调整窗口大小
        self.resize(window_width, window_height)

        # 居中显示
        self.move((screen_width - window_width) // 2,
                  (screen_height - window_height) // 2)

        # 如果屏幕太小，确保窗口不会超出屏幕边界
        if window_width > screen_width or window_height > screen_height:
            self.showMaximized()
            logging.info(f"屏幕尺寸较小 ({screen_width}x{screen_height})，窗口已最大化显示")

    def _setup_shortcuts(self):
        """设置键盘快捷键"""
        # Ctrl+O 打开文件
        open_shortcut = QKeySequence.Open
        open_action = QAction(self)
        open_action.setShortcut(open_shortcut)
        open_action.triggered.connect(self.select_image)
        self.addAction(open_action)

        # Ctrl+V 粘贴
        paste_shortcut = QKeySequence.Paste
        paste_action = QAction(self)
        paste_action.setShortcut(paste_shortcut)
        paste_action.triggered.connect(self.paste_image)
        self.addAction(paste_action)

        # Ctrl+C 复制结果
        copy_shortcut = QKeySequence.Copy
        copy_action = QAction(self)
        copy_action.setShortcut(copy_shortcut)
        copy_action.triggered.connect(self.copy_results)
        self.addAction(copy_action)

        # F5 刷新/重新识别
        refresh_action = QAction(self)
        refresh_action.setShortcut(QKeySequence.Refresh)
        refresh_action.triggered.connect(self.reprocess_current_image)
        self.addAction(refresh_action)

        # Ctrl+1/2/3 切换设备模式
        auto_action = QAction(self)
        auto_action.setShortcut(QKeySequence("Ctrl+1"))
        auto_action.triggered.connect(lambda: self.radio_auto.setChecked(True))
        self.addAction(auto_action)

        gpu_action = QAction(self)
        gpu_action.setShortcut(QKeySequence("Ctrl+2"))
        gpu_action.triggered.connect(lambda: self.radio_gpu.setChecked(True))
        self.addAction(gpu_action)

        cpu_action = QAction(self)
        cpu_action.setShortcut(QKeySequence("Ctrl+3"))
        cpu_action.triggered.connect(lambda: self.radio_cpu.setChecked(True))
        self.addAction(cpu_action)

        # Ctrl+4/5/6 切换模型版本
        v2_action = QAction(self)
        v2_action.setShortcut(QKeySequence("Ctrl+4"))
        v2_action.triggered.connect(lambda: self.radio_v2.setChecked(True))
        self.addAction(v2_action)

        v3_action = QAction(self)
        v3_action.setShortcut(QKeySequence("Ctrl+5"))
        v3_action.triggered.connect(lambda: self.radio_v3.setChecked(True))
        self.addAction(v3_action)

        v4_action = QAction(self)
        v4_action.setShortcut(QKeySequence("Ctrl+6"))
        v4_action.triggered.connect(lambda: self.radio_v4.setChecked(True))
        self.addAction(v4_action)

    def _show_initialization_message(self):
        """显示初始化消息"""
        init_text = (
            "🔄 正在初始化AI智能OCR识别工具...\n\n"
            "⏳ 请稍候，系统正在：\n"
            "• 🔧 加载OCR识别引擎\n"
            "• 🎯 检测可用的处理设备\n"
            "• 💾 初始化缓存系统\n"
            "• ⚡ 优化性能参数\n\n"
            "📝 提示：初始化完成后，您可以：\n"
            "• 拖拽图片到左侧区域\n"
            "• 使用 Ctrl+V 粘贴剪贴板图片\n"
            "• 点击\"选择图片\"按钮选择文件\n\n"
            "🚀 即将为您带来极速OCR体验！"
        )
        self.results_text.setPlainText(init_text)

    def _show_welcome_message(self):
        """显示欢迎消息 - 在图片区域显示"""
        welcome_text = (
            "🎉 欢迎使用AI智能OCR识别工具！\n\n"
            "✨ 新功能特性：\n"
            "• 🚀 智能缓存系统，相同图片秒速识别\n"
            "• 🎨 现代化UI设计，操作更流畅\n"
            "• ⚡ 图片预处理优化，识别更准确\n"
            "• 🔧 性能监控，实时显示处理统计\n\n"
            "⌨️ 快捷键：\n"
            "• Ctrl+O: 打开图片\n"
            "• Ctrl+V: 粘贴图片\n"
            "• Ctrl+C: 复制结果\n"
            "• F5: 重新识别\n"
            "• Ctrl+1/2/3: 切换设备模式\n"
            "• Ctrl+4/5/6: 切换模型版本\n"
            "• 鼠标滚轮: 缩放图片\n\n"
            "准备就绪，开始您的OCR之旅吧！ 🚀"
        )

        # 在图片区域显示欢迎信息
        self.image_label.setText(welcome_text)
        self.image_label.setStyleSheet("""
            QLabel {
                color: #1a73e8;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e3f2fd, stop:1 #bbdefb);
                border: 2px solid #2196f3;
                border-radius: 12px;
                font-family: "Microsoft YaHei", "Segoe UI";
                font-size: 14px;
                font-weight: 500;
                padding: 20px;
            }
        """)

        # 清空文字区域，显示简单提示
        self.results_text.setPlainText("📝 OCR识别结果将在此显示...")

    def _disable_controls_during_init(self):
        """初始化期间禁用控件并提供明确提示"""
        # 禁用所有操作按钮
        self.select_button.setEnabled(False)
        self.paste_button.setEnabled(False)
        self.fit_button.setEnabled(False)
        self.copy_button.setEnabled(False)
        self.clear_button.setEnabled(False)
        self.radio_auto.setEnabled(False)
        self.radio_gpu.setEnabled(False)
        self.radio_cpu.setEnabled(False)
        self.radio_v2.setEnabled(False)
        self.radio_v3.setEnabled(False)
        self.radio_v4.setEnabled(False)

        # 设置按钮提示文本，告知用户为什么不可用
        self.select_button.setToolTip("⏳ 正在初始化OCR引擎，请稍候...")
        self.paste_button.setToolTip("⏳ 正在初始化OCR引擎，请稍候...")
        self.fit_button.setToolTip("⏳ 正在初始化OCR引擎，请稍候...")
        self.copy_button.setToolTip("⏳ 正在初始化OCR引擎，请稍候...")
        self.clear_button.setToolTip("⏳ 正在初始化OCR引擎，请稍候...")

        # 设置设备选择提示
        self.radio_auto.setToolTip("⏳ 正在初始化OCR引擎，请稍候...")
        self.radio_gpu.setToolTip("⏳ 正在初始化OCR引擎，请稍候...")
        self.radio_cpu.setToolTip("⏳ 正在初始化OCR引擎，请稍候...")

        # 设置模型版本选择提示
        self.radio_v2.setToolTip("⏳ 正在初始化OCR引擎，请稍候...")
        self.radio_v3.setToolTip("⏳ 正在初始化OCR引擎，请稍候...")
        self.radio_v4.setToolTip("⏳ 正在初始化OCR引擎，请稍候...")

        # 更新状态提示
        self.stats_label.setText("📊 系统初始化中...")
        self.cache_label.setText("💾 缓存系统：正在初始化...")
        self.cache_label.setStyleSheet("color: #ffc107; font-size: 9pt; padding: 5px;")

        # 显示动画指示器
        self.show_status_animation("🚀 正在初始化AI引擎...")

        # 在图片区域显示初始化提示
        self.image_label.setText(
            "🔄 正在初始化OCR识别引擎...\n\n"
            "⏳ 首次运行需要下载模型文件\n"
            "📦 大小约300MB，请耐心等待\n"
            "🌐 需要稳定的网络连接\n\n"
            "💡 初始化完成后即可使用所有功能\n"
            "🚫 请勿在此期间操作按钮"
        )
        self.image_label.setStyleSheet("""
            QLabel {
                color: #f57c00;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fff3e0, stop:1 #ffe0b2);
                border: 2px solid #ff9800;
                border-radius: 12px;
                font-family: "Microsoft YaHei", "Segoe UI";
                font-size: 14px;
                font-weight: bold;
                padding: 20px;
            }
        """)

    def _enable_controls_after_init(self):
        """初始化完成后启用控件并恢复正常提示"""
        # 启用所有操作按钮
        self.select_button.setEnabled(True)
        self.paste_button.setEnabled(True)
        self.fit_button.setEnabled(True)
        self.copy_button.setEnabled(True)
        self.clear_button.setEnabled(True)
        self.radio_auto.setEnabled(True)
        self.radio_cpu.setEnabled(True)
        self.radio_v2.setEnabled(True)
        self.radio_v3.setEnabled(True)
        self.radio_v4.setEnabled(True)

        # GPU按钮根据支持情况启用
        if self.ocr_processor.check_gpu_support():
            self.radio_gpu.setEnabled(True)

        # 恢复正常的按钮提示文本
        self.select_button.setToolTip("选择本地图片文件 (Ctrl+O)")
        self.paste_button.setToolTip("粘贴剪贴板中的图片 (Ctrl+V)")
        self.fit_button.setToolTip("调整图片大小以适应窗口")
        self.copy_button.setToolTip("复制识别结果到剪贴板 (Ctrl+C)")
        self.clear_button.setToolTip("清空图片和结果")

        # 恢复设备选择提示
        self.radio_auto.setToolTip("自动检测最佳设备 (推荐)")
        self.radio_gpu.setToolTip("使用GPU加速处理 (需要CUDA支持)" if self.ocr_processor.check_gpu_support()
                                 else "❌ 未检测到GPU或CUDA未正确安装")
        self.radio_cpu.setToolTip("使用CPU处理 (兼容性最好)")

        # 恢复模型版本选择提示
        self.radio_v2.setToolTip("经典版本 - 轻量级，速度快")
        self.radio_v3.setToolTip("稳定版本 - 兼容性好，稳定可靠")
        self.radio_v4.setToolTip("推荐版本 - 性能和准确率最佳平衡，相比v3提升4.5%")

        # 更新状态提示
        self.stats_label.setText("📊 系统就绪，等待处理图片...")
        self.cache_label.setText("💾 缓存系统：已就绪")
        self.cache_label.setStyleSheet("color: #28a745; font-size: 9pt; padding: 5px;")

        # 隐藏动画指示器
        self.hide_status_animation()

        # 恢复图片区域的正常显示
        self.image_label.clearPixmap()  # 这会恢复默认的拖拽提示

    def show_status_animation(self, message):
        """显示状态栏动画 - 避免覆盖图片"""
        self.status_animation_message = message
        self.status_animation_dots = 0
        self.status_animation_timer.start(500)  # 每500ms更新一次
        self.update_status_animation()

    def hide_status_animation(self):
        """隐藏状态栏动画"""
        self.status_animation_timer.stop()

    def update_status_animation(self):
        """更新状态栏动画文字"""
        dots = "." * (self.status_animation_dots % 4)
        animated_message = f"{self.status_animation_message}{dots}"
        self.statusBar().showMessage(animated_message)
        self.status_animation_dots += 1

    def reprocess_current_image(self):
        """重新处理当前图片"""
        if self.current_image_path or self.current_image_data:
            # 清除缓存以强制重新处理
            if self.current_image_hash and self.cache_manager:
                if self.current_image_hash in self.cache_manager.ocr_cache:
                    del self.cache_manager.ocr_cache[self.current_image_hash]

            if self.current_image_path:
                self.load_and_process_image(image_path=self.current_image_path)
            elif self.current_image_data:
                self.load_and_process_image(image_data=self.current_image_data)

    def _create_widgets(self):
        """创建所有UI组件"""
        # 中央组件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # --- 左侧面板 (图片显示) ---
        self.image_scroll_area = QScrollArea()
        self.image_label = ZoomableImageLabel(self.cache_manager)
        self.image_label.set_scroll_area(self.image_scroll_area)
        self.image_label.set_main_window(self)  # 设置主窗口引用

        self.image_scroll_area.setWidget(self.image_label)
        self.image_scroll_area.setWidgetResizable(True)

        # --- 右侧面板 (控制和结果) ---
        # 设备选择组 - 紧凑样式
        self.device_groupbox = QGroupBox("设备")
        self.device_groupbox.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 11px;
                border: 1px solid #cccccc;
                border-radius: 4px;
                margin-top: 8px;
                padding-top: 4px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 4px 0 4px;
            }
        """)

        # 创建单选按钮 - 简化文本避免显示不全
        self.radio_auto = QRadioButton("智能")
        self.radio_gpu = QRadioButton("GPU")
        self.radio_cpu = QRadioButton("CPU")

        # 设置紧凑样式
        for radio in [self.radio_auto, self.radio_gpu, self.radio_cpu]:
            radio.setStyleSheet("QRadioButton { font-size: 11px; }")

        # 设置工具提示
        self.radio_auto.setToolTip("自动检测最佳设备 (推荐)")
        self.radio_gpu.setToolTip("使用GPU加速处理 (需要CUDA支持)")
        self.radio_cpu.setToolTip("使用CPU处理 (兼容性最好)")

        self.radio_auto.setChecked(True)

        self.device_button_group = QButtonGroup(self)
        self.device_button_group.addButton(self.radio_auto)
        self.device_button_group.addButton(self.radio_gpu)
        self.device_button_group.addButton(self.radio_cpu)

        if not self.ocr_processor.check_gpu_support():
            self.radio_gpu.setEnabled(False)
            self.radio_gpu.setToolTip("❌ 未检测到GPU或CUDA未正确安装")

        # 模型版本选择组 - 紧凑样式
        self.model_groupbox = QGroupBox("模型")
        self.model_groupbox.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 11px;
                border: 1px solid #cccccc;
                border-radius: 4px;
                margin-top: 8px;
                padding-top: 4px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 4px 0 4px;
            }
        """)

        # 创建模型版本单选按钮
        self.radio_v2 = QRadioButton("v2")
        self.radio_v3 = QRadioButton("v3")
        self.radio_v4 = QRadioButton("v4")

        # 设置紧凑样式
        for radio in [self.radio_v2, self.radio_v3, self.radio_v4]:
            radio.setStyleSheet("QRadioButton { font-size: 11px; }")

        # 设置详细的工具提示
        self.radio_v2.setToolTip("经典版本 - 轻量级，速度快")
        self.radio_v3.setToolTip("稳定版本 - 兼容性好，稳定可靠")
        self.radio_v4.setToolTip("推荐版本 - 性能和准确率最佳平衡，相比v3提升4.5%")

        # 默认选择PP-OCRv4（推荐）
        self.radio_v4.setChecked(True)

        self.model_button_group = QButtonGroup(self)
        self.model_button_group.addButton(self.radio_v2)
        self.model_button_group.addButton(self.radio_v3)
        self.model_button_group.addButton(self.radio_v4)

        # 结果文本区域
        self.results_text = QTextEdit()
        self.results_text.setPlaceholderText("✨ OCR识别结果将在此显示，支持快捷键操作...")
        self.results_text.setReadOnly(True)

        # 操作按钮 - 简化文本避免显示不全
        self.select_button = QPushButton("选择图片")
        self.select_button.setIcon(QIcon("icons/select_icon.svg"))
        self.select_button.setToolTip("选择本地图片文件 (Ctrl+O)")

        self.paste_button = QPushButton("粘贴图片")
        self.paste_button.setIcon(QIcon("icons/paste_icon.svg"))
        self.paste_button.setToolTip("粘贴剪贴板中的图片 (Ctrl+V)")

        self.fit_button = QPushButton("适应窗口")
        self.fit_button.setIcon(QIcon("icons/fit_icon.svg"))
        self.fit_button.setToolTip("调整图片大小以适应窗口")

        self.copy_button = QPushButton("复制结果")
        self.copy_button.setIcon(QIcon("icons/copy_icon.svg"))
        self.copy_button.setToolTip("复制识别结果到剪贴板 (Ctrl+C)")

        self.clear_button = QPushButton("清空所有")
        self.clear_button.setIcon(QIcon("icons/clear_icon.svg"))
        self.clear_button.setToolTip("清空图片和结果")

        # 性能统计标签
        self.stats_label = QLabel("📊 性能统计：等待处理...")
        self.stats_label.setStyleSheet("color: #6c757d; font-size: 9pt; padding: 5px;")

        # 缓存状态标签
        self.cache_label = QLabel("💾 缓存状态：已就绪")
        self.cache_label.setStyleSheet("color: #28a745; font-size: 9pt; padding: 5px;")

        # 状态栏动画定时器 - 简化方案，避免覆盖问题
        self.status_animation_timer = QTimer(self)
        self.status_animation_timer.timeout.connect(self.update_status_animation)
        self.status_animation_message = ""
        self.status_animation_dots = 0

    def _create_layouts(self):
        """组织组件布局"""
        # 设备选择布局 - 紧凑布局节省空间
        device_layout = QHBoxLayout(self.device_groupbox)
        device_layout.setContentsMargins(8, 8, 8, 6)  # 减少边距
        device_layout.setSpacing(8)  # 减少间距
        device_layout.addWidget(self.radio_auto)
        device_layout.addWidget(self.radio_gpu)
        device_layout.addWidget(self.radio_cpu)

        # 模型版本选择布局 - 紧凑布局
        model_layout = QHBoxLayout(self.model_groupbox)
        model_layout.setContentsMargins(8, 8, 8, 6)  # 减少边距
        model_layout.setSpacing(8)  # 减少间距
        model_layout.addWidget(self.radio_v2)
        model_layout.addWidget(self.radio_v3)
        model_layout.addWidget(self.radio_v4)

        # 按钮布局 - 紧凑的两行布局
        button_layout = QVBoxLayout()
        button_layout.setSpacing(8)

        # 第一行按钮 - 主要操作
        button_row1 = QHBoxLayout()
        button_row1.setSpacing(8)
        button_row1.addWidget(self.select_button)
        button_row1.addWidget(self.paste_button)
        button_row1.addWidget(self.copy_button)

        # 第二行按钮 - 辅助操作
        button_row2 = QHBoxLayout()
        button_row2.setSpacing(8)
        button_row2.addWidget(self.fit_button)
        button_row2.addWidget(self.clear_button)

        button_layout.addLayout(button_row1)
        button_layout.addLayout(button_row2)

        # 统计信息布局
        stats_layout = QVBoxLayout()
        stats_layout.setSpacing(5)
        stats_layout.addWidget(self.stats_label)
        stats_layout.addWidget(self.cache_label)

        # 创建控制面板容器 - 将设备和模型选择放在一行
        control_container = QWidget()
        control_layout = QHBoxLayout(control_container)
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(8)
        control_layout.addWidget(self.device_groupbox, 1)  # 设备选择
        control_layout.addWidget(self.model_groupbox, 1)   # 模型版本选择

        # 右侧面板布局 - 优化空间分配
        right_layout = QVBoxLayout()
        right_layout.setSpacing(8)  # 减少间距
        right_layout.setContentsMargins(12, 12, 12, 12)
        right_layout.addWidget(control_container)  # 控制面板（一行显示）
        right_layout.addWidget(self.results_text, 6)  # 文本框获得更多空间
        right_layout.addLayout(button_layout)  # 按钮区域（紧凑）
        right_layout.addLayout(stats_layout)  # 统计信息（最小）

        right_widget = QWidget()
        right_widget.setLayout(right_layout)
        right_widget.setMaximumWidth(550)  # 增加文字区域的最大宽度
        right_widget.setMinimumWidth(320)  # 适合小屏幕的最小宽度

        # 主分割器布局
        self.splitter = QSplitter(Qt.Horizontal)
        self.splitter.addWidget(self.image_scroll_area)
        self.splitter.addWidget(right_widget)
        self.splitter.setSizes([400, 600])  # 调整比例：图片区域更小，文字区域更大
        self.splitter.setHandleWidth(3)

        # 设置中央组件的主布局
        main_layout = QHBoxLayout(self.central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.addWidget(self.splitter)

    def _create_connections(self):
        """Connect signals and slots."""
        # Buttons
        self.select_button.clicked.connect(self.select_image)
        self.paste_button.clicked.connect(self.paste_image)
        self.fit_button.clicked.connect(self.fit_image_to_window)
        self.copy_button.clicked.connect(self.copy_results)
        self.clear_button.clicked.connect(self.clear_all)

        # OCR Engine Radio Buttons
        self.radio_auto.toggled.connect(self.reinitialize_ocr)
        self.radio_cpu.toggled.connect(self.reinitialize_ocr)
        self.radio_gpu.toggled.connect(self.reinitialize_ocr)

        # OCR Model Version Radio Buttons
        self.radio_v2.toggled.connect(self.reinitialize_ocr)
        self.radio_v3.toggled.connect(self.reinitialize_ocr)
        self.radio_v4.toggled.connect(self.reinitialize_ocr)
        
        # Install event filter for direct pasting on the image area
        self.image_scroll_area.installEventFilter(self)

    def _create_menu(self):
        """Create the main menu bar."""
        self.menu_bar = self.menuBar()
        # File Menu
        file_menu = self.menu_bar.addMenu("文件(&F)")
        select_action = QAction("选择图片(&S)...", self)
        select_action.triggered.connect(self.select_image)
        file_menu.addAction(select_action)

        paste_action = QAction("粘贴图片(&P)", self)
        paste_action.triggered.connect(self.paste_image)
        file_menu.addAction(paste_action)

        file_menu.addSeparator()
        exit_action = QAction("退出(&X)", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Help Menu
        help_menu = self.menu_bar.addMenu("帮助(&H)")
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)


    # --- Slots and Event Handlers ---

    def eventFilter(self, watched, event):
        """Filter events for specific widgets, like handling paste shortcut."""
        if watched == self.image_scroll_area and event.type() == QEvent.KeyPress:
            if event.matches(QKeySequence.Paste):
                self.paste_image()
                return True  # Event was handled
        
        return super().eventFilter(watched, event)

    @Slot()
    def reinitialize_ocr(self, checked=True):
        """在后台线程中初始化OCR"""
        if not isinstance(checked, bool) or not checked:
            return

        if not PADDLE_AVAILABLE:
            self.statusBar().showMessage("❌ 错误: 未找到 PaddleOCR 库")
            QMessageBox.critical(self, "缺少依赖库",
                               "未安装 PaddleOCR。此应用需要该库才能运行。\n\n"
                               "请运行以下命令安装：\n"
                               "pip install paddlepaddle paddleocr")
            self._enable_controls_after_init()  # 即使失败也启用控件
            return

        device_type = 'auto'
        device_name = '智能检测'
        if self.radio_cpu.isChecked():
            device_type = 'cpu'
            device_name = 'CPU模式'
        elif self.radio_gpu.isChecked():
            device_type = 'gpu'
            device_name = 'GPU加速'

        # 获取选择的OCR版本
        ocr_version = 'PP-OCRv4'  # 默认版本
        version_name = 'PP-OCRv4'
        if self.radio_v2.isChecked():
            ocr_version = 'PP-OCRv2'
            version_name = 'PP-OCRv2'
        elif self.radio_v3.isChecked():
            ocr_version = 'PP-OCRv3'
            version_name = 'PP-OCRv3'

        # 显示切换提示
        self.statusBar().showMessage(f"🔄 正在切换到 {device_name} ({version_name})...")

        # 临时禁用设备选择按钮，避免重复切换
        self.radio_auto.setEnabled(False)
        self.radio_gpu.setEnabled(False)
        self.radio_cpu.setEnabled(False)

        # 显示切换动画
        self.show_status_animation(f"🔄 正在切换到 {device_name} ({version_name})...")

        # 保存当前文本内容，不清除已识别的文字
        current_text = self.results_text.toPlainText()

        # 如果当前有识别结果，保持显示；否则显示切换提示
        if not current_text or current_text.startswith(('🎉', '🔄', '📊', '✅')):
            switch_text = (
                f"🔄 正在切换处理模式...\n\n"
                f"📋 当前选择：{device_name} + {version_name}\n"
                f"⏳ 正在重新初始化OCR引擎\n\n"
                f"💡 设备模式：\n"
                f"• 智能检测：自动选择最佳设备\n"
                f"• GPU加速：使用显卡加速处理\n"
                f"• CPU模式：使用处理器处理\n\n"
                f"🤖 模型版本：\n"
                f"• PP-OCRv2：经典版本，轻量级\n"
                f"• PP-OCRv3：稳定版本，兼容性好\n"
                f"• PP-OCRv4：推荐版本，性能平衡\n\n"
                f"请稍候，切换完成后即可继续使用..."
            )
            self.results_text.setPlainText(switch_text)

        # 确保之前的线程已经清理
        if self.thread and self.thread.isRunning():
            self.thread.quit()
            self.thread.wait()

        # 只在首次初始化时显示弹窗
        if self.is_first_init:
            self.loading_dialog = QLoadingDialog(
                self, message=f"🤖 正在初始化 {device_name} + {version_name} 引擎...\n\n这可能需要几秒钟时间",
                show_progress=True
            )
            # 延迟显示对话框
            QTimer.singleShot(100, self.loading_dialog.show)

        self.thread = QThread()
        self.worker = OcrWorker(self.ocr_processor, device_type=device_type, ocr_version=ocr_version)
        self.worker.moveToThread(self.thread)

        self.thread.started.connect(self.worker.run)
        self.worker.finished.connect(self.on_ocr_initialization_finished)
        self.thread.start()

    @Slot(bool, str)
    def on_ocr_initialization_finished(self, success, message):
        """处理OCR初始化完成（线程安全）"""
        # 确保在主线程中执行UI操作
        def update_ui():
            if self.loading_dialog:
                self.loading_dialog.close()
                self.loading_dialog = None

            # 隐藏动画
            self.hide_status_animation()

            # 继续处理其他UI更新...
            self._handle_initialization_result(success, message)

        # 如果不在主线程，则调度到主线程执行
        if threading.current_thread() != threading.main_thread():
            QTimer.singleShot(0, update_ui)
        else:
            update_ui()

    def _handle_initialization_result(self, success, message):
        """处理初始化结果的UI更新"""

        if success:
            # 获取当前设备类型用于显示
            device_name = '智能检测'
            if self.radio_cpu.isChecked():
                device_name = 'CPU模式'
            elif self.radio_gpu.isChecked():
                device_name = 'GPU加速'

            # 获取当前模型版本
            version_name = 'PP-OCRv4'
            if self.radio_v2.isChecked():
                version_name = 'PP-OCRv2'
            elif self.radio_v3.isChecked():
                version_name = 'PP-OCRv3'

            self.statusBar().showMessage(f"✅ {device_name} + {version_name} 初始化成功！", 8000)
            self._enable_controls_after_init()

            # 检查当前文本内容，如果有识别结果就保持，否则显示欢迎信息
            current_text = self.results_text.toPlainText()
            if not current_text or current_text.startswith(('🔄', '📊', '🎉')):
                # 只有在没有识别结果时才显示欢迎信息
                self._show_welcome_message()
        else:
            self.statusBar().showMessage(f"❌ 初始化失败: {message}", 10000)
            QMessageBox.critical(self, "OCR 初始化错误",
                               f"初始化 OCR 引擎失败:\n{message}\n\n"
                               "请检查：\n"
                               "• PaddleOCR 是否正确安装\n"
                               "• 网络连接是否正常\n"
                               "• 系统资源是否充足")
            # 即使失败也要启用基本控件
            self._enable_controls_after_init()
            self._show_welcome_message()

        # 标记非首次初始化
        self.is_first_init = False

        self.thread.quit()
        self.thread.wait()

    @Slot()
    def select_image(self):
        """Opens a file dialog to select an image."""
        # 检查OCR是否已初始化
        if not self.ocr_processor.is_initialized:
            QMessageBox.information(
                self,
                "系统初始化中",
                "🔄 OCR引擎正在初始化中，请稍候...\n\n"
                "⏳ 首次运行需要下载模型文件\n"
                "📦 大小约300MB，请耐心等待\n"
                "🌐 需要稳定的网络连接\n\n"
                "💡 初始化完成后会自动提示，届时即可正常使用所有功能！"
            )
            return

        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择图片", "", "图片文件 (*.png *.jpg *.jpeg *.bmp *.webp)"
        )
        if file_path:
            self.load_and_process_image(image_path=file_path)

    @Slot()
    def paste_image(self):
        """Handles pasting an image from the clipboard."""
        # 检查OCR是否已初始化
        if not self.ocr_processor.is_initialized:
            QMessageBox.information(
                self,
                "系统初始化中",
                "🔄 OCR引擎正在初始化中，请稍候...\n\n"
                "⏳ 首次运行需要下载模型文件\n"
                "📦 大小约300MB，请耐心等待\n"
                "🌐 需要稳定的网络连接\n\n"
                "💡 初始化完成后会自动提示，届时即可正常使用所有功能！"
            )
            return

        try:
            clipboard_content = ImageGrab.grabclipboard()

            if isinstance(clipboard_content, Image.Image):
                # 剪贴板中是图片数据
                import io
                img_byte_arr = io.BytesIO()
                clipboard_content.save(img_byte_arr, format='PNG')
                image_data = img_byte_arr.getvalue()
                self.load_and_process_image(image_data=image_data)

            elif isinstance(clipboard_content, list) and len(clipboard_content) > 0:
                # 剪贴板中是文件路径列表
                file_path = clipboard_content[0]
                if isinstance(file_path, str) and file_path.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.webp', '.tiff')):
                    if os.path.exists(file_path):
                        self.load_and_process_image(image_path=file_path)
                        self.statusBar().showMessage(f"📋 已从剪贴板加载图片文件: {os.path.basename(file_path)}", 5000)
                    else:
                        self.statusBar().showMessage("❌ 剪贴板中的图片文件不存在", 5000)
                else:
                    self.statusBar().showMessage("❌ 剪贴板中的文件不是支持的图片格式", 5000)
            else:
                self.statusBar().showMessage("📋 剪贴板中没有找到图片或图片文件", 5000)

        except Exception as e:
            self.statusBar().showMessage(f"❌ 粘贴图片时出错: {e}", 5000)

    def load_and_process_image(self, image_path=None, image_data=None):
        """加载、显示并开始OCR处理图片"""
        self.results_text.clear()
        pixmap = QPixmap()

        try:
            # 准备图片数据
            if image_path:
                self.current_image_path = image_path
                self.current_image_data = None

                # 检查文件是否存在
                if not os.path.exists(image_path):
                    self.statusBar().showMessage("❌ 错误：图片文件不存在", 5000)
                    return

                # 读取文件数据用于哈希计算
                try:
                    with open(image_path, 'rb') as f:
                        file_data = f.read()
                    self.current_image_hash = self.cache_manager.get_image_hash(file_data)
                except Exception as e:
                    self.statusBar().showMessage(f"❌ 读取文件失败: {e}", 5000)
                    return

                pixmap.load(image_path)
                self.statusBar().showMessage(f"📁 已加载图片: {os.path.basename(image_path)}", 5000)

            elif image_data:
                self.current_image_path = None
                self.current_image_data = image_data
                self.current_image_hash = self.cache_manager.get_image_hash(image_data)

                pixmap.loadFromData(image_data)
                self.statusBar().showMessage("📋 已从剪贴板加载图片", 5000)
            else:
                return

            if pixmap.isNull():
                self.statusBar().showMessage("❌ 错误：无效的图片文件或格式不支持", 5000)
                return

        except Exception as e:
            self.statusBar().showMessage(f"❌ 加载图片时出错: {e}", 5000)
            logging.error(f"加载图片失败: {e}")
            return

        # 检查缓存
        cached_result = self.cache_manager.get_cached_ocr_result(self.current_image_hash)
        if cached_result:
            self.results_text.setPlainText(cached_result['result'])
            self.statusBar().showMessage("⚡ 使用缓存结果，瞬间完成！", 3000)
            self.update_cache_status(True)
            self.image_label.setPixmap(pixmap, self.current_image_hash)
            self.fit_image_to_window()
            return

        # 显示图片并开始处理
        self.image_label.setPixmap(pixmap, self.current_image_hash)
        self.fit_image_to_window()
        self.results_text.setPlainText("🔍 正在智能识别中，请稍候...")
        self.update_cache_status(False)
        self.start_ocr_processing()

    def update_cache_status(self, is_cache_hit):
        """更新缓存状态显示"""
        if is_cache_hit:
            self.cache_label.setText("💾 缓存命中：使用已缓存结果")
            self.cache_label.setStyleSheet("color: #28a745; font-size: 9pt; padding: 5px;")
        else:
            self.cache_label.setText("💾 缓存状态：处理新图片...")
            self.cache_label.setStyleSheet("color: #ffc107; font-size: 9pt; padding: 5px;")

    def update_performance_stats(self, processing_time=None):
        """更新性能统计显示"""
        if processing_time:
            self.performance_stats['images_processed'] += 1
            self.performance_stats['total_processing_time'] += processing_time

        total_images = self.performance_stats['images_processed']
        total_time = self.performance_stats['total_processing_time']
        session_time = time.time() - self.performance_stats['session_start']

        if total_images > 0:
            avg_time = total_time / total_images
            cache_hits = self.ocr_processor.stats['cache_hits']
            cache_rate = (cache_hits / self.ocr_processor.stats['total_processed']) * 100 if self.ocr_processor.stats['total_processed'] > 0 else 0

            stats_text = (f"📊 已处理: {total_images}张 | "
                         f"平均耗时: {avg_time:.1f}s | "
                         f"缓存命中率: {cache_rate:.1f}% | "
                         f"会话时长: {session_time/60:.1f}分钟")
        else:
            stats_text = f"📊 会话时长: {session_time/60:.1f}分钟 | 等待处理图片..."

        self.stats_label.setText(stats_text)

    def start_ocr_processing(self):
        """在后台线程中开始OCR处理"""
        if not (self.current_image_path or self.current_image_data) or not self.ocr_processor.is_initialized:
            self.statusBar().showMessage("❌ OCR引擎未就绪或没有图片", 5000)
            return

        # 确保之前的线程已经清理
        if self.thread and self.thread.isRunning():
            self.thread.quit()
            self.thread.wait()

        self.statusBar().showMessage("🔍 正在智能处理图片...")

        # OCR处理时不显示覆盖动画，只使用加载对话框
        self.loading_dialog = QLoadingDialog(self, message="🤖 AI正在识别文字，请稍候...", show_progress=True)
        self.loading_dialog.show()

        self.thread = QThread()
        self.worker = OcrWorker(self.ocr_processor,
                               image_path=self.current_image_path,
                               image_data=self.current_image_data)
        self.worker.moveToThread(self.thread)

        self.thread.started.connect(self.worker.run)
        self.worker.finished.connect(self.on_ocr_processing_finished)
        self.thread.start()

    @Slot(bool, str)
    def on_ocr_processing_finished(self, success, result):
        """处理OCR识别完成的结果"""
        if self.loading_dialog:
            self.loading_dialog.close()
            self.loading_dialog = None

        self.results_text.setPlainText(result)

        if success:
            # 更新性能统计
            processing_time = getattr(self.worker, 'processing_time', 0)
            self.update_performance_stats(processing_time)

            # 更新缓存状态
            self.update_cache_status(False)
            self.cache_label.setText("💾 缓存状态：结果已缓存")
            self.cache_label.setStyleSheet("color: #28a745; font-size: 9pt; padding: 5px;")

            # 显示成功消息
            char_count = len(result.replace('\n', '').replace(' ', ''))
            self.statusBar().showMessage(f"✅ 识别完成！共识别 {char_count} 个字符", 5000)
        else:
            self.statusBar().showMessage(f"❌ 处理失败: {result}", 8000)
            self.update_cache_status(False)

        self.thread.quit()
        self.thread.wait()

    @Slot()
    def copy_results(self):
        """复制识别结果到剪贴板"""
        text = self.results_text.toPlainText()
        if text and not text.startswith(('🎉', '🔍', '📊')):  # 排除欢迎消息和状态消息
            pyperclip.copy(text)
            char_count = len(text.replace('\n', '').replace(' ', ''))
            self.statusBar().showMessage(f"📋 已复制 {char_count} 个字符到剪贴板", 3000)
        else:
            self.statusBar().showMessage("⚠️ 没有可复制的识别结果", 3000)

    @Slot()
    def clear_all(self):
        """清空图片、结果和状态"""
        self.image_label.clearPixmap()
        self.current_image_path = None
        self.current_image_data = None
        self.current_image_hash = None

        # 显示欢迎消息
        self._show_welcome_message()

        # 重置统计信息
        self.cache_label.setText("💾 缓存状态：已就绪")
        self.cache_label.setStyleSheet("color: #28a745; font-size: 9pt; padding: 5px;")

        self.statusBar().showMessage("🗑️ 已清空所有内容", 3000)

    @Slot()
    def fit_image_to_window(self):
        """Fits the image to the available window size."""
        if self.image_label._pixmap.isNull():
            return
        
        viewport_size = self.image_scroll_area.viewport().size()
        pixmap_size = self.image_label._pixmap.size()
        
        if pixmap_size.isEmpty():
            return
            
        viewport_ratio = viewport_size.width() / viewport_size.height()
        pixmap_ratio = pixmap_size.width() / pixmap_size.height()

        if viewport_ratio > pixmap_ratio:
            new_scale = viewport_size.height() / pixmap_size.height()
        else:
            new_scale = viewport_size.width() / pixmap_size.width()
        
        self.image_label.scale_factor = new_scale * 0.98 # Add a small margin
        self.image_label.update_scaled_pixmap()

    def show_about(self):
        """显示关于对话框"""
        about_text = """
        <div style='text-align: center;'>
            <h2 style='color: #1a73e8; margin-bottom: 20px;'>🔍 AI智能OCR识别工具</h2>
            <p style='font-size: 14px; color: #495057; margin-bottom: 15px;'>
                <strong>高性能版本 v2.0</strong>
            </p>

            <div style='text-align: left; margin: 20px 0;'>
                <h3 style='color: #28a745;'>✨ 核心特性</h3>
                <ul style='color: #6c757d; line-height: 1.6;'>
                    <li>🚀 智能缓存系统，相同图片瞬间识别</li>
                    <li>🎨 现代化UI设计，操作体验流畅</li>
                    <li>⚡ GPU/CPU自适应，性能最优化</li>
                    <li>🔧 图片预处理增强，识别更准确</li>
                    <li>📊 实时性能监控，处理状态可视化</li>
                    <li>⌨️ 丰富快捷键支持，操作更高效</li>
                </ul>

                <h3 style='color: #dc3545;'>🛠️ 技术栈</h3>
                <ul style='color: #6c757d; line-height: 1.6;'>
                    <li>界面框架：PySide6 (Qt6)</li>
                    <li>OCR引擎：PaddleOCR</li>
                    <li>图像处理：PIL/Pillow</li>
                    <li>缓存系统：自研高效缓存</li>
                </ul>
            </div>

            <p style='color: #6c757d; font-size: 12px; margin-top: 20px;'>
                © 2024 AI智能工具集 | 让AI为您的工作提效
            </p>
        </div>
        """

        msg = QMessageBox(self)
        msg.setWindowTitle("关于 AI智能OCR识别工具")
        msg.setText(about_text)
        msg.setIcon(QMessageBox.Information)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.exec()


    # --- Event Handlers for Drag-n-Drop ---
    def dragEnterEvent(self, event):
        mime_data = event.mimeData()
        if mime_data.hasUrls() and len(mime_data.urls()) == 1:
            url = mime_data.urls()[0]
            # Check for image file extensions
            if url.isLocalFile() and url.toLocalFile().lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):
                # 检查OCR是否已初始化
                if not self.ocr_processor.is_initialized:
                    event.ignore()
                    return
                event.acceptProposedAction()

    def dropEvent(self, event):
        # 检查OCR是否已初始化
        if not self.ocr_processor.is_initialized:
            QMessageBox.information(
                self,
                "系统初始化中",
                "🔄 OCR引擎正在初始化中，请稍候...\n\n"
                "⏳ 首次运行需要下载模型文件\n"
                "📦 大小约300MB，请耐心等待\n"
                "🌐 需要稳定的网络连接\n\n"
                "💡 初始化完成后会自动提示，届时即可正常使用所有功能！"
            )
            return

        url = event.mimeData().urls()[0]
        file_path = url.toLocalFile()
        self.load_and_process_image(image_path=file_path)

    def resizeEvent(self, event):
        """Handle window resize events, e.g., to refit image."""
        super().resizeEvent(event)

    def closeEvent(self, event):
        """应用程序关闭时的清理工作"""
        try:
            # 停止状态动画
            if hasattr(self, 'status_animation_timer'):
                self.status_animation_timer.stop()

            # 清理线程
            if self.thread and self.thread.isRunning():
                self.thread.quit()
                self.thread.wait(3000)  # 等待最多3秒
                if self.thread.isRunning():
                    self.thread.terminate()

            # 关闭加载对话框
            if self.loading_dialog:
                self.loading_dialog.close()

            # 保存缓存
            if hasattr(self, 'cache_manager'):
                self.cache_manager._save_ocr_cache()

        except Exception as e:
            logging.warning(f"关闭应用时出现错误: {e}")

        super().closeEvent(event)
        
# --- Main execution ---
def main():
    """Main function to run the application."""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Global exception hook
    def excepthook(exc_type, exc_value, exc_tb):
        import traceback
        tb_text = "".join(traceback.format_exception(exc_type, exc_value, exc_tb))
        logging.error(f"Unhandled exception:\n{tb_text}")
        QMessageBox.critical(None, "致命错误", f"发生了一个未处理的严重错误:\n{exc_value}\n\n详情请查看日志。")

    sys.excepthook = excepthook

    app = QApplication(sys.argv)
    window = OCRApp()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
