from PIL import Image
import os
import tkinter as tk
from tkinter import filedialog, messagebox
from tkinter import ttk
import threading
from tkinter.ttk import Progressbar
import json
import os.path

class ImageSplicerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("图片竖向拼接工具")
        self.config_file = os.path.join(os.path.dirname(__file__), 'config.json')
        
        # 加载配置
        self.load_config()
        
        # 设置窗口大小并居中
        window_width = 600
        window_height = 200
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # 创建主框架
        main_frame = ttk.Frame(root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置grid权重
        root.grid_rowconfigure(0, weight=1)
        root.grid_columnconfigure(0, weight=1)
        main_frame.grid_columnconfigure(1, weight=1)
        
        # 输入文件夹选择
        ttk.Label(main_frame, text="输入文件夹:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.input_path = tk.StringVar(value=self.config.get('input_path', ''))
        input_entry = ttk.Entry(main_frame, textvariable=self.input_path, width=50)
        input_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.EW)
        ttk.Button(main_frame, text="浏览", command=self.select_input_folder).grid(row=0, column=2, pady=5)
        
        # 输出文件选择
        ttk.Label(main_frame, text="输出文件:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.output_path = tk.StringVar(value=self.config.get('output_path', ''))
        output_entry = ttk.Entry(main_frame, textvariable=self.output_path, width=50)
        output_entry.grid(row=1, column=1, padx=5, pady=5, sticky=tk.EW)
        ttk.Button(main_frame, text="浏览", command=self.select_output_file).grid(row=1, column=2, pady=5)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='determinate')  # 改为确定性进度条
        self.progress.grid(row=2, column=0, columnspan=3, sticky=tk.EW, pady=10)
        self.progress.grid_remove()
        
        # 进度标签
        self.progress_label = ttk.Label(main_frame, text="")
        self.progress_label.grid(row=2, column=0, columnspan=3, sticky=tk.EW, pady=10)
        self.progress_label.grid_remove()
        
        # 开始拼接按钮
        self.splice_button = ttk.Button(main_frame, text="开始拼接", command=self.start_splicing_thread)
        self.splice_button.grid(row=3, column=1, pady=10)
        
    def load_config(self):
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = {'input_path': '', 'output_path': ''}
                self.save_config()
        except Exception:
            self.config = {'input_path': '', 'output_path': ''}
    
    def save_config(self):
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"保存配置文件失败: {e}")

    def select_input_folder(self):
        folder_path = filedialog.askdirectory()
        if folder_path:
            self.input_path.set(folder_path)
            self.config['input_path'] = folder_path
            self.save_config()
            
    def select_output_file(self):
        file_path = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG files", "*.png"), ("JPEG files", "*.jpg"), ("All files", "*.*")]
        )
        if file_path:
            self.output_path.set(file_path)
            self.config['output_path'] = file_path
            self.save_config()

    def start_splicing_thread(self):
        input_folder = self.input_path.get()
        output_path = self.output_path.get()
        
        if not input_folder or not output_path:
            messagebox.showerror("错误", "请选择输入文件夹和输出文件路径")
            return
            
        # 检查是否有图片文件
        image_files = [f for f in os.listdir(input_folder) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp'))]
        if not image_files:
            messagebox.showerror("错误", "未找到图片文件！")
            return
            
        self.splice_button.configure(state='disabled')
        self.progress.grid()  # 显示进度条
        self.progress.start(10)
        
        thread = threading.Thread(target=self.start_splicing)
        thread.daemon = True
        thread.start()

    def finish_splicing(self):
        self.progress.stop()
        self.progress.grid_remove()  # 隐藏进度条
        self.splice_button.configure(state='normal')
            
    def splice_images_vertically(self, input_folder, output_path):
        image_files = [f for f in os.listdir(input_folder) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp'))]
        
        if not image_files:
            raise Exception("未找到图片文件！")
        
        # 设置进度条最大值
        total_files = len(image_files)
        self.root.after(0, lambda: self.progress.configure(maximum=total_files))
        
        # 预先计算总高度和最大宽度
        total_height = 0
        max_width = 0
        images = []
        
        for i, img_file in enumerate(image_files):
            img = Image.open(os.path.join(input_folder, img_file))
            images.append(img)
            total_height += img.height
            max_width = max(max_width, img.width)
            # 更新进度
            progress = (i + 1) / total_files * 100
            self.root.after(0, lambda p=progress: self.update_progress(p, f"正在加载图片 {i+1}/{total_files}"))
        
        # 修改创建新图片的代码，使用 RGBA 模式
        result = Image.new('RGBA', (max_width, total_height), (0, 0, 0, 0))  # 最后的0表示完全透明
        
        # 拼接图片
        current_height = 0
        for i, img in enumerate(images):
            # 确保图片有 alpha 通道
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
                
            if img.width < max_width:
                x_offset = (max_width - img.width) // 2
            else:
                x_offset = 0
                
            result.paste(img, (x_offset, current_height), img)  # 添加 mask 参数
            current_height += img.height
            # 更新进度
            progress = 50 + (i + 1) / total_files * 50  # 拼接过程占后50%的进度
            self.root.after(0, lambda p=progress: self.update_progress(p, f"正在拼接图片 {i+1}/{total_files}"))
        
        # 保存结果
        self.root.after(0, lambda: self.update_progress(100, ""))
        result.save(output_path)

    def update_progress(self, value, text):
        self.progress['value'] = value
        if text:  # 只有在有文本时才显示和更新标签
            self.progress_label['text'] = text
            self.progress_label.grid()
        else:
            self.progress_label.grid_remove()  # 当文本为空时隐藏标签

    def start_splicing(self):
        input_folder = self.input_path.get()
        output_path = self.output_path.get()
        
        try:
            self.splice_images_vertically(input_folder, output_path)
            self.root.after(0, lambda: messagebox.showinfo("成功", "图片拼接完成！"))
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"处理过程中出现错误：{str(e)}"))
        finally:
            self.root.after(0, self.finish_splicing)

    # 删除第二个 start_splicing_thread 方法（重复定义）

def main():
    root = tk.Tk()
    app = ImageSplicerApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()