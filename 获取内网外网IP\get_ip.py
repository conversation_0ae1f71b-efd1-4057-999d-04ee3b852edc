import tkinter as tk
from tkinter import ttk, messagebox
import socket
import requests
import threading
from datetime import datetime
import json
import os

class IPFetcher:
    def __init__(self):
        self.is_fetching = False
        self.create_main_window()
        self.load_history()
        
    def create_main_window(self):
        self.root = tk.Tk()
        self.root.title("IP地址查询工具")
        
        # 设置窗口大小和样式
        window_width = 450
        window_height = 380
        self.center_window(window_width, window_height)
        self.root.configure(bg='#f5f6fa')
        
        # 设置图标
        try:
            self.root.iconbitmap("获取内网外网IP/sync.ico")
        except:
            pass
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 设置样式
        style = ttk.Style()
        style.configure('TButton', padding=6, font=('微软雅黑', 10))
        style.configure('Accent.TButton', font=('微软雅黑', 10, 'bold'))
        style.configure('TLabel', font=('微软雅黑', 10))
        style.configure('TLabelframe', background='#f5f6fa')
        style.configure('TLabelframe.Label', font=('微软雅黑', 10, 'bold'))
        style.configure('TFrame', background='#f5f6fa')
        
        # IP显示区域
        ip_frame = ttk.LabelFrame(main_frame, text="IP地址信息", padding="15")
        ip_frame.pack(fill=tk.X, pady=10)
        
        # 内网IP
        ttk.Label(ip_frame, text="内网地址：").grid(row=0, column=0, sticky='w', pady=5)
        self.local_ip_box = ttk.Entry(ip_frame, width=35, state='readonly', font=('Consolas', 10))
        self.local_ip_box.grid(row=0, column=1, padx=5, pady=5)
        
        # 外网IP
        ttk.Label(ip_frame, text="外网地址：").grid(row=1, column=0, sticky='w', pady=5)
        self.public_ip_box = ttk.Entry(ip_frame, width=35, state='readonly', font=('Consolas', 10))
        self.public_ip_box.grid(row=1, column=1, padx=5, pady=5)
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        self.fetch_button = ttk.Button(
            button_frame,
            text="获取IP地址",
            command=self.start_ip_fetch,
            style='Accent.TButton'
        )
        self.fetch_button.pack(side=tk.LEFT, padx=5)
        
        self.copy_button = ttk.Button(
            button_frame,
            text="复制全部",
            command=self.copy_all,
            style='TButton'
        )
        self.copy_button.pack(side=tk.LEFT, padx=5)
        
        # 历史记录区域
        history_frame = ttk.LabelFrame(main_frame, text="查询历史", padding="15")
        history_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 添加历史记录显示和滚动条
        history_container = ttk.Frame(history_frame)
        history_container.pack(fill=tk.BOTH, expand=True)
        
        self.history_text = tk.Text(
            history_container, 
            height=8, 
            width=40,
            font=('微软雅黑', 9),
            bg='#ffffff',
            relief='flat',
            padx=8,
            pady=8
        )
        scrollbar = ttk.Scrollbar(history_container, orient="vertical", command=self.history_text.yview)
        self.history_text.configure(yscrollcommand=scrollbar.set)
        
        self.history_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_bar = ttk.Label(
            self.root,
            textvariable=self.status_var,
            relief=tk.FLAT,
            anchor=tk.W,
            background='#e8e8e8',
            padding=(5, 2)
        )
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM, pady=2)
        self.status_var.set("就绪")
        
        # 绑定右键菜单
        self.create_context_menu()
        
    def create_context_menu(self):
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="复制", command=self.copy_selected)
        
        for widget in (self.local_ip_box, self.public_ip_box):
            widget.bind("<Button-3>", self.show_context_menu)
            widget.bind("<Control-c>", self.copy_selected)
    
    def load_history(self):
        # 使用脚本所在目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.history_file = os.path.join(script_dir, "ip_history.json")
        
        if os.path.exists(self.history_file):
            try:
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    self.history = json.load(f)
            except:
                self.history = []
        else:
            self.history = []
        self.update_history_display()
    
    def save_history(self, local_ip, public_ip):
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.history.insert(0, {
            "time": current_time,
            "local_ip": local_ip,
            "public_ip": public_ip
        })
        # 只保留最近10条记录
        self.history = self.history[:10]
        
        with open(self.history_file, 'w', encoding='utf-8') as f:
            json.dump(self.history, f, ensure_ascii=False, indent=2)
        
        self.update_history_display()
    
    def update_history_display(self):
        self.history_text.config(state=tk.NORMAL)
        self.history_text.delete(1.0, tk.END)
        for record in self.history:
            self.history_text.insert(tk.END, 
                f"时间: {record['time']}\n"
                f"内网IP: {record['local_ip']}\n"
                f"外网IP: {record['public_ip']}\n"
                f"{'-'*40}\n"
            )
        self.history_text.config(state=tk.DISABLED)
    
    def get_local_ip(self):
        try:
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            return local_ip
        except Exception as e:
            print(f"获取内网IP地址时出错: {e}")
            return "无法获取"

    def get_public_ip(self):
        try:
            response = requests.get("https://ipinfo.io/json", timeout=5)
            if response.status_code == 200:
                data = response.json()
                return data.get('ip', "无法获取")
            return "无法获取"
        except Exception as e:
            print(f"获取外网IP地址时出错: {e}")
            return "无法获取"

    def start_ip_fetch(self):
        if self.is_fetching:
            messagebox.showinfo("提示", "正在获取IP地址，请稍候...")
            return
            
        self.is_fetching = True
        self.fetch_button.config(state=tk.DISABLED)
        self.status_var.set("正在获取IP地址...")
        
        # 清空显示框
        self.local_ip_box.config(state=tk.NORMAL)
        self.public_ip_box.config(state=tk.NORMAL)
        self.local_ip_box.delete(0, tk.END)
        self.public_ip_box.delete(0, tk.END)
        self.local_ip_box.insert(0, "获取中...")
        self.public_ip_box.insert(0, "获取中...")
        self.local_ip_box.config(state='readonly')
        self.public_ip_box.config(state='readonly')
        
        threading.Thread(target=self.fetch_ip, daemon=True).start()

    def fetch_ip(self):
        local_ip = self.get_local_ip()
        public_ip = self.get_public_ip()
        
        self.root.after(0, self.update_ip_display, local_ip, public_ip)
        
    def update_ip_display(self, local_ip, public_ip):
        self.local_ip_box.config(state=tk.NORMAL)
        self.public_ip_box.config(state=tk.NORMAL)
        
        self.local_ip_box.delete(0, tk.END)
        self.public_ip_box.delete(0, tk.END)
        
        self.local_ip_box.insert(0, local_ip)
        self.public_ip_box.insert(0, public_ip)
        
        self.local_ip_box.config(state='readonly')
        self.public_ip_box.config(state='readonly')
        
        self.save_history(local_ip, public_ip)
        
        self.is_fetching = False
        self.fetch_button.config(state=tk.NORMAL)
        self.status_var.set("IP地址获取完成")

    def copy_all(self):
        text = f"内网IP：{self.local_ip_box.get()}\n外网IP：{self.public_ip_box.get()}"
        self.root.clipboard_clear()
        self.root.clipboard_append(text)
        self.status_var.set("已复制所有IP地址到剪贴板")

    def copy_selected(self, event=None):
        widget = self.root.focus_get()
        if widget in (self.local_ip_box, self.public_ip_box):
            self.root.clipboard_clear()
            self.root.clipboard_append(widget.get())
            self.status_var.set("已复制选中内容到剪贴板")

    def show_context_menu(self, event):
        widget = event.widget
        widget.focus_set()
        self.context_menu.post(event.x_root, event.y_root)

    def center_window(self, width, height):
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = IPFetcher()
    app.run()
