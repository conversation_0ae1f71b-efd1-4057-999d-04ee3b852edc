import requests
from bs4 import BeautifulSoup
import time
import tkinter as tk
from tkinter import ttk, messagebox
import threading
from tkinter import font

class JavbusSearchApp:
    def __init__(self, root):
        self.root = root
        self.root.title("番号查询")
        self.root.configure(bg='#f0f0f0')
        self.setup_ui()
        self.center_window()
        
    def setup_ui(self):
        # 设置样式
        style = ttk.Style()
        style.configure('Custom.TButton', padding=5, font=('Microsoft YaHei', 9))
        style.configure('Custom.TLabel', font=('Microsoft YaHei', 10))
        style.configure('Custom.TEntry', padding=5)

        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 输入区域
        input_frame = ttk.Frame(main_frame)
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        entry_label = ttk.Label(input_frame, text="请输入番号:", style='Custom.TLabel')
        entry_label.pack(side=tk.LEFT, padx=5)
        
        self.entry = ttk.Entry(input_frame, width=40, style='Custom.TEntry')
        self.entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.entry.bind('<Return>', lambda e: self.process_input())

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=5)
        
        self.search_button = ttk.Button(button_frame, text="查询", 
                                      command=self.process_input, 
                                      style='Custom.TButton')
        self.search_button.pack(side=tk.LEFT, padx=5)
        
        self.copy_button = ttk.Button(button_frame, text="复制结果", 
                                    command=self.copy_result, 
                                    style='Custom.TButton')
        self.copy_button.pack(side=tk.LEFT, padx=5)

        # 状态标签
        self.status_label = ttk.Label(main_frame, text="", style='Custom.TLabel')
        self.status_label.pack(fill=tk.X, pady=5)

        # 结果文本框
        self.result_text = tk.Text(main_frame, height=10, width=50, 
                                 font=('Microsoft YaHei', 10),
                                 wrap=tk.WORD)
        self.result_text.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, 
                                command=self.result_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.result_text.configure(yscrollcommand=scrollbar.set)

    def center_window(self):
        self.root.update_idletasks()
        width = 450
        height = 350
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def process_input(self):
        input_text = self.entry.get().strip()
        if not input_text:
            messagebox.showwarning("提示", "请输入番号！")
            return

        self.search_button.configure(state='disabled')
        self.status_label.configure(text="正在查询...")
        self.result_text.delete(1.0, tk.END)
        
        thread = threading.Thread(target=self.search_task, args=(input_text,))
        thread.daemon = True
        thread.start()

    def search_task(self, input_text, max_retries=3):
        for attempt in range(max_retries):
            try:
                url = f"https://www.javbus.com/{input_text}"
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9'
                }

                response = requests.get(url, headers=headers, timeout=10)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    container_div = soup.find('div', class_='container')
                    if container_div:
                        star_name_divs = container_div.find_all('div', class_='star-name')
                        if star_name_divs:
                            result = "\n".join([div.get_text(strip=True) 
                                             for div in star_name_divs])
                            self.update_ui("success", result)
                            return
                        else:
                            self.update_ui("error", "未找到演员信息")
                            return
                    else:
                        self.update_ui("error", "页面结构不符")
                        return
                else:
                    if attempt < max_retries - 1:
                        self.update_status(f"请求失败，正在重试 ({attempt + 1}/{max_retries})")
                        time.sleep(1)
                        continue
                    self.update_ui("error", f"请求失败，状态码: {response.status_code}")
                    return

            except Exception as e:
                if attempt < max_retries - 1:
                    self.update_status(f"发生错误，正在重试 ({attempt + 1}/{max_retries})")
                    time.sleep(1)
                    continue
                self.update_ui("error", f"查询失败: {str(e)}")
                return

    def update_ui(self, status, message):
        self.root.after(0, lambda: self._update_ui_internal(status, message))

    def _update_ui_internal(self, status, message):
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, message)
        self.status_label.configure(text="查询完成" if status == "success" else "查询失败")
        self.search_button.configure(state='normal')

    def update_status(self, message):
        self.root.after(0, lambda: self.status_label.configure(text=message))

    def copy_result(self):
        result = self.result_text.get(1.0, tk.END).strip()
        if result:
            self.root.clipboard_clear()
            self.root.clipboard_append(result)
            messagebox.showinfo("提示", "已复制到剪贴板！")
        else:
            messagebox.showwarning("提示", "没有可复制的内容！")

def main():
    root = tk.Tk()
    app = JavbusSearchApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()