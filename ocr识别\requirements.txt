# OCR应用依赖包清单
# 使用命令安装: pip install -r requirements.txt

# === 核心应用框架 ===
pyside6==6.7.0

# === 图像处理和剪贴板操作 ===
Pillow==10.3.0
pyperclip==1.8.2

# === OCR引擎和深度学习框架 ===
numpy==1.26.4
paddleocr==2.7.3
opencv-python==********

# === PaddlePaddle深度学习框架 ===
# 根据硬件选择以下其中一个版本：

# CPU版本（默认，兼容性最好）
paddlepaddle==2.6.1

# GPU版本（需要NVIDIA GPU和CUDA支持）
# 如需GPU加速，请注释上面的CPU版本，取消注释下面的GPU版本
# paddlepaddle-gpu==2.6.1.post118

# === 安装说明 ===
# 1. GPU版本需要：
#    - NVIDIA GPU（支持CUDA）
#    - CUDA Toolkit 11.8或12.x
#    - 对应的cuDNN库
#
# 2. 如果GPU版本安装失败，请：
#    - 检查CUDA版本兼容性
#    - 回退到CPU版本
#    - 更新显卡驱动
#
# 3. Windows用户可能需要：
#    - Microsoft Visual C++ Redistributable
#    - 最新的Windows更新
